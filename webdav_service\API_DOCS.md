# WebDAV Service API Documentation

## 上传音频文件接口

### POST /upload

用户直接向WebDAV服务发送上传请求，包含歌曲信息和WebDAV配置。

#### 请求参数

| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| song_hash | string | 是 | 歌曲的SHA256验证哈希值（用于后端验证） |
| song_title | string | 是 | 歌曲标题 |
| song_artist | string | 是 | 艺术家名称 |
| album | string | 是 | 专辑名称 |
| video_id | string | 是 | 视频ID（由用户传入） |
| format | string | 是 | 目标音频格式（mp3, flac, wav, aac, m4a, ogg） |
| cover_url | string | 是 | 封面图片的完整URL |
| webdav_config | object | 是 | WebDAV配置对象 |

#### webdav_config 对象

| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| id | integer | 是 | 配置ID |
| url | string | 是 | WebDAV服务器URL |
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |
| signature | string | 是 | 预生成的配置签名 |

#### 请求示例

```json
{
    "song_hash": "abc123def456789...",
    "song_title": "示例歌曲",
    "song_artist": "示例艺术家",
    "album": "示例专辑",
    "video_id": "dQw4w9WgXcQ",
    "format": "mp3",
    "cover_url": "https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",
    "webdav_config": {
        "id": 1,
        "url": "https://webdav.example.com",
        "username": "user",
        "password": "password",
        "signature": "webdav_config_signature"
    }
}
```

#### 响应

**成功响应 (200)**
```json
{
    "success": true,
    "message": "Upload task started",
    "task_id": "uuid-task-id"
}
```

**错误响应 (400)**
```json
{
    "error": "Missing required fields"
}
```

**错误响应 (403)**
```json
{
    "error": "Invalid webdav config signature"
}
```

#### 处理流程

1. **验证请求参数**：检查所有必需字段是否存在
2. **验证WebDAV配置签名**：确保配置未被篡改
3. **启动异步任务**：创建Celery任务处理上传
4. **返回任务ID**：用户可以通过任务ID跟踪进度

#### 异步任务流程

1. **轮询下载状态**：向后端请求下载链接（最多20次，间隔2秒）
2. **下载原始音频**：获取webm格式的原始文件
3. **下载封面图片**：使用cover_sign下载webp格式封面
4. **处理封面图片**：裁剪为正方形并转换为jpg格式
5. **转码音频文件**：使用FFmpeg转码为目标格式并嵌入封面
6. **上传到WebDAV**：将处理后的文件上传到用户的WebDAV服务器
7. **清理临时文件**：删除所有临时文件

## 添加WebDAV配置接口

### POST /add_config

用户携带JWT令牌向WebDAV服务发送添加配置请求。

#### 请求参数

| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| jwt_token | string | 是 | 用户的WebDAV JWT令牌 |
| url | string | 是 | WebDAV服务器URL |
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |

#### 请求示例

```json
{
    "jwt_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "url": "https://webdav.example.com",
    "username": "user",
    "password": "password"
}
```

#### 响应

**成功响应 (200)**
```json
{
    "success": true,
    "message": "WebDAV configuration added successfully",
    "config_id": 1,
    "signature": "generated_signature"
}
```

**错误响应 (400)**
```json
{
    "error": "WebDAV connection failed: Authentication failed"
}
```

**错误响应 (401)**
```json
{
    "error": "Invalid JWT token"
}
```

## 重要说明

### 签名机制

1. **WebDAV配置签名**：由后端生成并存储，用于验证配置完整性
2. **封面图片签名**：由用户从后端获取并传入，用于下载封面图片

### 安全考虑

1. **JWT验证**：添加配置时需要有效的JWT令牌
2. **签名验证**：上传时验证WebDAV配置签名
3. **连接测试**：添加配置前测试WebDAV连接有效性

### 错误处理

- 网络错误：自动重试机制
- 文件不存在：跳过可选文件（如封面图片）
- 转码失败：返回详细错误信息
- WebDAV上传失败：重试机制
