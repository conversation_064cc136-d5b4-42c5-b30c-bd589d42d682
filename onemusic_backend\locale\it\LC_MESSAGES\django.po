# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "La musica non esiste"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "Richiesta di download della canzone fallita"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "La canzone è in fase di download"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "Attività di download creata"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "Richiesta di download della canzone inviata, disponibile più tardi"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "Metodo di richiesta non valido"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "Chiave non valida"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "Valore hash della canzone mancante"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "Stato del download aggiornato"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "La canzone è pronta per il download, non è necessario un altro aggiornamento"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "Canzone non trovata"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "Registro di download non trovato"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "Registro di download eliminato con successo"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "Attività di upload eliminata con successo"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "L'email, la password e il codice di verifica non possono essere vuoti"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "Verifica del codice fallita, riprovare"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "Errore nell'intestazione dell'email, invio fallito"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "Email di verifica inviata, controlla la tua casella di posta."

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "Link non valido o scaduto"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "Dati di verifica incompleti"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "Email o password errata"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "Configurazione utente non trovata"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "È consentito un massimo di 5 configurazioni"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "Connessione WebDAV fallita: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "Configurazione non trovata"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "Campi obbligatori mancanti"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "Richiesta di download fallita"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "Richiesta di upload fallita"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "Attività di upload creata"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "Configurazione WebDAV non valida"

#: backend/webdav.py:145
msgid "未授权"
msgstr "Non autorizzato"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "Attività non trovata"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "Cinese semplificato"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr "Arabo"

#: onemusic_backend/settings.py:133
msgid "German"
msgstr "Tedesco"

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "Inglese"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr "Spagnolo"

#: onemusic_backend/settings.py:136
msgid "French"
msgstr "Francese"

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr "Hindi"

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr "Italiano"

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr "Giapponese"

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr "Coreano"

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr "Olandese"

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr "Portoghese"

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr "Russo"

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr "Turco"

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr "Cinese tradizionale"

