# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: hi\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "संगीत मौजूद नहीं है"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "गीत डाउनलोड करने का अनुरोध विफल"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "गीत डाउनलोड हो रहा है"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "डाउनलोड कार्य बनाया गया"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "गीत डाउनलोड अनुरोध भेजा गया, बाद में उपलब्ध होगा"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "अमान्य अनुरोध विधि"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "अमान्य कुंजी"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "गीत हैश मान गायब"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "डाउनलोड स्थिति अपडेट की गई"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "गीत डाउनलोड के लिए तैयार है, दोबारा अपडेट की जरूरत नहीं"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "गीत नहीं मिला"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "डाउनलोड रिकॉर्ड नहीं मिला"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "डाउनलोड रिकॉर्ड सफलतापूर्वक हटाया गया"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "अपलोड कार्य सफलतापूर्वक हटाया गया"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "ईमेल, पासवर्ड और सत्यापन कोड खाली नहीं हो सकते"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "सत्यापन कोड सत्यापन विफल, कृपया पुनः प्रयास करें"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "ईमेल हेडर त्रुटि, भेजने में विफल"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "सत्यापन ईमेल भेजा गया, कृपया अपनी इनबॉक्स जांचें।"

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "लिंक अमान्य या समाप्त हो गया है"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "सत्यापन डेटा अधूरा है"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "ईमेल या पासवर्ड गलत है"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "उपयोगकर्ता कॉन्फ़िगरेशन नहीं मिला"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "अधिकतम 5 कॉन्फ़िगरेशन की अनुमति है"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "WebDAV कनेक्शन विफल: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "कॉन्फ़िगरेशन नहीं मिला"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "आवश्यक फ़ील्ड गायब हैं"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "डाउनलोड अनुरोध विफल"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "अपलोड अनुरोध विफल"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "अपलोड कार्य बनाया गया"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "अमान्य WebDAV कॉन्फ़िगरेशन"

#: backend/webdav.py:145
msgid "未授权"
msgstr "अनधिकृत"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "कार्य नहीं मिला"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "सरलीकृत चीनी"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr "अरबी"

#: onemusic_backend/settings.py:133
msgid "German"
msgstr "जर्मन"

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "अंग्रेज़ी"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr "स्पेनिश"

#: onemusic_backend/settings.py:136
msgid "French"
msgstr "फ्रेंच"

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr "हिंदी"

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr "इतालवी"

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr "जापानी"

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr "कोरियाई"

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr "डच"

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr "पुर्तगाली"

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr "रूसी"

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr "तुर्की"

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr "पारंपरिक चीनी"

