import hashlib
import json
import hmac
import jwt
from datetime import timedelta, datetime
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils.translation import gettext as _
from onemusic_backend.settings import WEBDAV_JWT_SECRET, WEBDAV_JWT_EXPIRATION_HOURS
from .models import WebdavConfig, Profile, UploadTask, Music, UserDownloadedMusic

# WebDAV通信专用密钥
WEBDAV_SECRET_KEY = '7pERYJmLTgimM499yB6ZYptKEFTmr3jBkoowFzP5Za'

def generate_webdav_config_signature(config_data):
    """
    为webdav配置生成SHA256签名
    """
    # 将配置数据按固定顺序组合
    config_string = f"{config_data['url']}|{config_data['username']}|{config_data['password']}|{config_data['config_id']}"

    # 使用HMAC-SHA256生成签名
    signature = hmac.new(
        WEBDAV_SECRET_KEY.encode('utf-8'),
        config_string.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()

    return signature

def generate_webdav_jwt(user_email):
    """
    生成WebDAV JWT令牌
    """
    payload = {
        'email': user_email,
        'exp': datetime.utcnow() + timedelta(hours=WEBDAV_JWT_EXPIRATION_HOURS),
        'iat': datetime.utcnow(),
        'iss': '1music_webdav'
    }

    token = jwt.encode(payload, WEBDAV_JWT_SECRET, algorithm='HS256')
    return token

def verify_webdav_jwt(token):
    """
    验证WebDAV JWT令牌
    """
    try:
        payload = jwt.decode(token, WEBDAV_JWT_SECRET, algorithms=['HS256'])
        return True, payload
    except Exception:
        return False, None




@csrf_exempt
@login_required(login_url='/login')
@require_http_methods(["POST"])
def remove_webdav_config_view(request):
    config_id = json.loads(request.body)['config_id']
    try:
        config = WebdavConfig.objects.get(id=config_id, profile__user=request.user)
        config.delete()
        return JsonResponse({'success': True})
    except WebdavConfig.DoesNotExist:
        return JsonResponse({'success': False, 'error': _('配置未找到')}, status=404)





@csrf_exempt
@require_http_methods(["POST"])
def webdav_add_config_callback_view(request):
    """
    WebDAV服务添加配置回调接口
    """
    data = json.loads(request.body)

    # 验证密钥
    if data.get("secret") != WEBDAV_SECRET_KEY:
        return JsonResponse({"error": _('未授权')}, status=401)

    # 验证JWT
    jwt_token = data.get("jwt_token")
    if not jwt_token:
        return JsonResponse({"error": _('缺少JWT令牌')}, status=400)

    is_valid, payload = verify_webdav_jwt(jwt_token)
    if not is_valid:
        return JsonResponse({"error": _('无效的JWT令牌')}, status=401)

    # 获取用户
    try:
        from django.contrib.auth.models import User
        user = User.objects.get(username=payload['email'])
        profile = get_object_or_404(Profile, user=user)
    except User.DoesNotExist:
        return JsonResponse({"error": _('用户不存在')}, status=404)

    # 检查配置数量限制
    if profile.webdav_config.count() >= 5:
        return JsonResponse({"error": _('最多允许5个配置')}, status=400)

    # 创建配置
    required_fields = ['url', 'username', 'password']
    if not all(field in data for field in required_fields):
        return JsonResponse({"error": _('缺少必要字段')}, status=400)

    config = WebdavConfig.objects.create(
        profile=profile,
        url=data['url'],
        username=data['username'],
        password=data['password'],
        is_tested=True  # 由WebDAV服务验证过
    )

    # 生成并保存签名
    config_data = {
        'config_id': config.id,
        'url': config.url,
        'username': config.username,
        'password': config.password
    }
    signature = generate_webdav_config_signature(config_data)
    config.signature = signature
    config.save()

    return JsonResponse({"success": True, "config_id": config.id, "signature": signature})

@csrf_exempt
@require_http_methods(["POST"])
def create_upload_task_view(request):
    """
    创建上传任务接口 - 供WebDAV服务调用
    """
    data = json.loads(request.body)

    # 验证密钥
    if data.get("secret") != WEBDAV_SECRET_KEY:
        return JsonResponse({"error": _('未授权')}, status=401)

    # 验证JWT
    jwt_token = data.get("jwt_token")
    if not jwt_token:
        return JsonResponse({"error": _('缺少JWT令牌')}, status=400)

    is_valid, payload = verify_webdav_jwt(jwt_token)
    if not is_valid:
        return JsonResponse({"error": _('无效的JWT令牌')}, status=401)

    # 获取用户
    try:
        from django.contrib.auth.models import User
        user = User.objects.get(username=payload['email'])
    except User.DoesNotExist:
        return JsonResponse({"error": _('用户不存在')}, status=404)

    # 验证必要字段
    required_fields = ['song_title', 'song_artist', 'album', 'format_type', 'webdav_config_id']
    if not all(field in data for field in required_fields):
        return JsonResponse({"error": _('缺少必要字段')}, status=400)

    try:
        # 根据歌曲信息生成MD5 hash
        song_hash = hashlib.md5(
            (data['song_title'] + data['album'] + data['song_artist']).encode('utf-8')
        ).hexdigest()

        # 获取已存在的Music记录
        music = get_object_or_404(Music, hash=song_hash)

        # 获取WebDAV配置
        webdav_config = get_object_or_404(WebdavConfig, id=data['webdav_config_id'])

        # 创建上传任务（使用自增ID）
        upload_task = UploadTask.objects.create(
            user=user,
            music=music,
            webdav_config=webdav_config,
            format_type=data['format_type'],
            status='processing'
        )

        # 将歌曲添加到用户下载记录中（如果还没有的话）
        UserDownloadedMusic.objects.get_or_create(user=user, music=music)

        # 增加歌曲下载计数
        music.download_count += 1
        music.save()

        return JsonResponse({"success": True, "task_id": upload_task.id})

    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def update_upload_task_status_view(request):
    """
    更新上传任务状态接口 - 供WebDAV服务调用
    """
    data = json.loads(request.body)

    # 验证密钥
    if data.get("secret") != WEBDAV_SECRET_KEY:
        return JsonResponse({"error": _('未授权')}, status=401)

    task_id = data.get("task_id")
    status = data.get("status")
    reason = data.get("reason", None)

    if not task_id or not status:
        return JsonResponse({"error": _('缺少必要字段')}, status=400)

    try:
        task = UploadTask.objects.get(id=task_id)
        task.status = status
        if reason:
            task.reason = reason
        task.save()
        return JsonResponse({"success": True})
    except UploadTask.DoesNotExist:
        return JsonResponse({"error": _('任务未找到')}, status=404)





