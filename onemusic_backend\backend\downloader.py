import hashlib
from datetime import timedelta

import requests
from django.http import JsonResponse, HttpResponseNotFound
from django.contrib.auth.decorators import login_required
from django.utils.timezone import now
from django.views.decorators.csrf import csrf_exempt

from onemusic_backend.settings import DOWNLOADER_SECRET, DOWNLOAD_SERVER_URL, DEFAULT_DOWNLOAD_PARTITION
from .models import Music, UserDownloadedMusic
from django.utils.translation import gettext as _
import json

from backend.utils import generate_secure_hash_salt


@csrf_exempt
def download_music_view(request):
    if request.method == 'POST':
        data = json.loads(request.body)
        # 验证歌曲哈希值
        expected_hash = hashlib.sha256(
            (data['title'] + data['album'] + data['artist'] + data['videoId'] + DOWNLOADER_SECRET).encode('utf-8')
        ).hexdigest()

        if expected_hash != data['song_hash']:
            return HttpResponseNotFound(json.dumps({'detail': _('音乐不存在')}), content_type='application/json')

        # 获取歌曲的 MD5 哈希值
        song_hash = hashlib.md5((data['title'] + data['album'] + data['artist']).encode('utf-8')).hexdigest()

        try:
            # 检查数据库中是否已存在该歌曲
            music = Music.objects.get(hash=song_hash)
            if not music.could_download:
                # 检查是否超过 2 分钟
                if now() - music.created > timedelta(minutes=2):
                    music.created = now()
                    music.save()
                    # 重新发送下载请求
                    response = requests.post(f"{DOWNLOAD_SERVER_URL}/download", json={
                        'music_hash': song_hash,
                        'title': data['title'],
                        'artist' : data['artist'],
                        'album' : data['album'],
                        'request_format': data['request_format'],
                        'video_id': data['videoId'],
                        'partition': music.partition,
                        'secret': DOWNLOADER_SECRET
                    })
                    if response.status_code != 200:
                        music.delete()
                        return JsonResponse({'error': _('请求歌曲下载失败')}, status=500)
                return JsonResponse({'detail': _('下载任务已创建')}, status=202)

            # 如果用户已登录，添加歌曲到下载记录
            if request.user.is_authenticated:
                UserDownloadedMusic.objects.get_or_create(user=request.user, music=music)
                music.download_count += 1
                music.save()

            # 直接生成原始webm文件的下载链接（前端负责转码）
            # 生成安全的hash_salt，使用webm格式（原始格式）
            hash_salt = generate_secure_hash_salt(music.partition, music.hash, 'webm')
            # 生成新的下载链接格式：[host]/[分区]/[hash]/webm?salt=[hash_salt]
            download_url = f"{DOWNLOAD_SERVER_URL}/{music.partition}/{music.hash}/webm?salt={hash_salt}"

            return JsonResponse({'detail': _('下载任务已创建'), 'download_url': download_url})

        except Music.DoesNotExist:
            # 添加新歌曲到数据库，状态设为 "下载中"
            music = Music.objects.create(
                hash=song_hash,
                title=data['title'],
                album=data['album'],
                artist=data['artist'],
                partition=DEFAULT_DOWNLOAD_PARTITION,
                could_download=False
            )

            # 发送下载请求
            response = requests.post(f"{DOWNLOAD_SERVER_URL}/download", json={
                        'music_hash': song_hash,
                        'title': data['title'],
                        'artist' : data['artist'],
                        'album' : data['album'],
                        'request_format': data['request_format'],
                        'video_id': data['videoId'],
                        'partition': music.partition,
                        'secret': DOWNLOADER_SECRET
                    })

            if response.status_code != 200:
                music.delete()
                return JsonResponse({'error': _('请求歌曲下载失败')}, status=500)

            return JsonResponse({'detail': _('歌曲下载请求已提交，稍后可用')}, status=202)

    return JsonResponse({'error': _('无效的请求方法')}, status=405)


@csrf_exempt
def preview_music_view(request):
    if request.method == 'POST':
        data = json.loads(request.body)

        # 验证歌曲哈希值
        expected_hash = hashlib.sha256(
            (data['title'] + data['album'] + data['artist'] + data['videoId'] + DOWNLOADER_SECRET).encode('utf-8')
        ).hexdigest()

        if expected_hash != data['song_hash']:
            return HttpResponseNotFound(json.dumps({'detail': _('音乐不存在')}), content_type='application/json')

        # 查询数据库中是否已有该歌曲
        song_hash = hashlib.md5((data['title'] + data['album'] + data['artist']).encode('utf-8')).hexdigest()
        try:
            music = Music.objects.get(hash=song_hash)
            if not music.could_download:
                # 检查是否超过 2 分钟
                if now() - music.created > timedelta(minutes=2):
                    music.created = now()
                    music.save()
                    # 重新发送下载请求
                    response = requests.post(f"{DOWNLOAD_SERVER_URL}/download", json={
                        'music_hash': song_hash,
                        'title': data['title'],
                        'artist' : data['artist'],
                        'album' : data['album'],
                        'request_format': data['request_format'],
                        'video_id': data['videoId'],
                        'partition': music.partition,
                        'secret': DOWNLOADER_SECRET
                    })
                    if response.status_code != 200:
                        music.delete()
                        return JsonResponse({'error': _('请求歌曲下载失败')}, status=500)
                return JsonResponse({'detail': _('下载任务已创建')}, status=202)
            # 生成安全的hash_salt
            hash_salt = generate_secure_hash_salt(music.partition, music.hash, 'webm')
            # 生成新的下载链接格式：[host]/[分区]/[hash]/webm?salt=[hash_salt]
            download_url = f"{DOWNLOAD_SERVER_URL}/{music.partition}/{music.hash}/webm?salt={hash_salt}"
            return JsonResponse({'detail': _('下载任务已创建'), 'download_url': download_url})

        except Music.DoesNotExist:
            # 添加新歌曲到数据库，状态设为 "下载中"
            music = Music.objects.create(
                hash=song_hash,
                title=data['title'],
                album=data['album'],
                artist=data['artist'],
                partition=DEFAULT_DOWNLOAD_PARTITION,
                could_download=False
            )
            # 发送下载请求
            response = requests.post(f"{DOWNLOAD_SERVER_URL}/download", json={
                'music_hash': song_hash,
                'title': data['title'],
                'artist' : data['artist'],
                'album' : data['album'],
                'request_format': data['request_format'],
                'video_id': data['videoId'],
                'partition': music.partition,
                'secret': DOWNLOADER_SECRET
            })
            if response.status_code != 200:
                music.delete()
                return JsonResponse({'error': _('请求歌曲下载失败')}, status=500)
            return JsonResponse({'detail': _('歌曲下载请求已提交，稍后可用')}, status=202)

    return JsonResponse({'error': _('无效的请求方法')}, status=405)


@csrf_exempt
def update_download_status(request):
    if request.method == 'POST':
        data = json.loads(request.body)

        # 验证下载器提供的密钥
        if data.get('secret') != DOWNLOADER_SECRET:
            return JsonResponse({'error': _('无效的密钥')}, status=403)

        song_hash = data.get('hash')
        if not song_hash:
            return JsonResponse({'error': _('缺少歌曲哈希值')}, status=400)

        try:
            music = Music.objects.get(hash=song_hash)
            if not music.could_download:
                music.could_download = True
                music.save()
                return JsonResponse({'detail': _('下载状态已更新')})
            else:
                return JsonResponse({'detail': _('歌曲已准备好下载，无需重复更新')}, status=200)

        except Music.DoesNotExist:
            return JsonResponse({'error': _('歌曲未找到')}, status=404)

    return JsonResponse({'error': _('无效的请求方法')}, status=405)