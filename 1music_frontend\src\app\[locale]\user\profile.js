import {useState} from "react";
import { useTranslations } from "next-intl";
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import NoSsr from '@mui/material/NoSsr';
import axios from "axios";
import {backendUrl, webdavServiceUrl} from "@/src/app/[locale]/config";
import RechargeDialog from "@/src/app/[locale]/component/rechargeDialog";

const ProfilePage = ({userData}) => {
    const t = useTranslations('ProfilePage');
    const username = userData.username;
    const points = userData.points;
    const [isLoading,setLoading] = useState(false);
    const [webDavConfigs, setWebDavConfigs] = useState(userData.webdav_configs);
    const [webdavJwt] = useState(userData.webdav_jwt); // WebDAV JWT令牌

    const handleWebDavChange = (index, field, value) => {
        const updatedConfigs = [...webDavConfigs];
        updatedConfigs[index][field] = value;
        setWebDavConfigs(updatedConfigs);
    };

    const handleAddWebDav = () => {
        if (webDavConfigs.length < 5) {
            setWebDavConfigs([...webDavConfigs, { url: '', username: '', password: '', isTested: false }]);
        }
    };

    const handleRemoveWebDav = async (index) => {
        const config = webDavConfigs[index];
        if (config.isTested) {
            try {
                await axios.post(backendUrl + 'remove_webdav_config/',{'config_id':config.id},{
                    withCredentials:true
                });
            } catch (error) {
                alert(error.response?.data?.error || t('remove_failed'));
                return;
            }
        }
        setWebDavConfigs(webDavConfigs.filter((_, i) => i !== index));
    };

    const handleTestAndSaveConnection = async (index) => {
        const config = webDavConfigs[index];
        if (config.url && config.username && config.password) {
            setLoading(true);
            try {
                // 调用WebDAV服务的add_config接口
                const response = await axios.post(webdavServiceUrl + 'add_config', {
                    jwt_token: webdavJwt,
                    url: config.url,
                    username: config.username,
                    password: config.password,
                },{
                    withCredentials:true
                });

                if (response.data.success) {
                    // 更新配置状态，使用WebDAV服务返回的数据
                    const updatedConfigs = [...webDavConfigs];
                    updatedConfigs[index] = {
                        id: response.data.config_id,
                        url: config.url,
                        username: config.username,
                        password: config.password,
                        signature: response.data.signature,
                        isTested: true
                    };
                    setWebDavConfigs(updatedConfigs);
                    setLoading(false);
                    alert(t('config_saved'));
                } else {
                    throw new Error(response.data.error || t('test_failed'));
                }
            } catch (error) {
                setLoading(false);
                alert(error.response?.data?.error || error.message || t('test_failed'));
            }
        } else {
            alert(t('incomplete_config'));
        }
    };

    return (
        <NoSsr>
            <Box p={3} display="flex" flexDirection="column" gap={3} maxWidth={600} margin="auto">
                <Card variant="outlined">
                    <CardContent>
                        <Typography variant="h6" gutterBottom>{t('user_info')}</Typography>
                        <Typography variant="body1" color="text.secondary">{username}{points > 100 ? t('premium') : ''}</Typography>
                        <RechargeDialog />
                    </CardContent>
                </Card>
                {webDavConfigs.map((config, index) => (
                    <Card variant="outlined" key={index}>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>{t('webdav_config', { index: index + 1 })}</Typography>
                            <TextField
                                label={t('webdav_url')}
                                variant="outlined"
                                fullWidth
                                size="small"
                                value={config.url}
                                onChange={(e) => handleWebDavChange(index, 'url', e.target.value)}
                                placeholder={t('webdav_url_placeholder')}
                                sx={{ mt: 2,mb: 2 }}
                            />
                            <TextField
                                label={t('username')}
                                variant="outlined"
                                fullWidth
                                size="small"
                                value={config.username}
                                onChange={(e) => handleWebDavChange(index, 'username', e.target.value)}
                                placeholder={t('username_placeholder')}
                                sx={{ mb: 2 }}
                            />
                            <TextField
                                label={t('password')}
                                variant="outlined"
                                type="password"
                                fullWidth
                                size="small"
                                value={config.password}
                                onChange={(e) => handleWebDavChange(index, 'password', e.target.value)}
                                placeholder={t('password_placeholder')}
                                sx={{ mb: 2 }}
                            />
                            <Box display="flex" justifyContent="space-between" alignItems="center">
                                {!config.isTested ? (
                                    <Button
                                        disabled={isLoading}
                                        color="primary"
                                        onClick={() => handleTestAndSaveConnection(index)}>
                                        {t('test_and_save')}
                                    </Button>
                                ) : (
                                    <Button
                                        color="success"
                                        >
                                        {t('connection_success')}
                                    </Button>
                                )}
                                <Button
                                    variant="text"
                                    color="error"
                                    onClick={() => handleRemoveWebDav(index)}
                                >
                                    {t('delete_config')}
                                </Button>
                            </Box>
                        </CardContent>
                    </Card>
                ))}

                {webDavConfigs.length < 5 && (
                    <Button variant="outlined" color="primary" onClick={handleAddWebDav}>
                        {webDavConfigs.length > 0 ? t('add_another_webdav') : t('add_webdav')}
                    </Button>
                )}
            </Box>
        </NoSsr>
    );
};

export default ProfilePage;

