# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: ko\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "음악이 존재하지 않습니다"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "노래 다운로드 요청 실패"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "노래를 다운로드 중입니다"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "다운로드 작업이 생성되었습니다"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "노래 다운로드 요청이 제출되었습니다. 나중에 이용할 수 있습니다"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "잘못된 요청 방법"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "잘못된 키"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "노래 해시 값이 없습니다"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "다운로드 상태가 업데이트되었습니다"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "노래가 다운로드 준비 완료되었습니다. 다시 업데이트할 필요가 없습니다"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "노래를 찾을 수 없습니다"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "다운로드 기록을 찾을 수 없습니다"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "다운로드 기록이 성공적으로 삭제되었습니다"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "업로드 작업이 성공적으로 삭제되었습니다"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "이메일, 비밀번호 및 인증 코드는 비워둘 수 없습니다"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "인증 코드 검증 실패. 다시 시도하세요"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "메일 헤더 오류로 인해 전송 실패"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "인증 메일이 전송되었습니다. 받은 편지함을 확인하세요"

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "링크가 잘못되었거나 만료되었습니다"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "인증 데이터가 불완전합니다"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "이메일 또는 비밀번호가 잘못되었습니다"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "사용자 설정을 찾을 수 없습니다"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "최대 5개의 설정이 허용됩니다"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "WebDAV 연결 실패: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "설정을 찾을 수 없습니다"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "필수 필드가 누락되었습니다"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "다운로드 요청 실패"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "업로드 요청 실패"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "업로드 작업이 생성되었습니다"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "잘못된 WebDAV 설정"

#: backend/webdav.py:145
msgid "未授权"
msgstr "승인되지 않음"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "작업을 찾을 수 없습니다"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "간체 중국어"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr "아랍어"

#: onemusic_backend/settings.py:133
msgid "German"
msgstr "독일어"

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "영어"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr "스페인어"

#: onemusic_backend/settings.py:136
msgid "French"
msgstr "프랑스어"

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr "힌디어"

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr "이탈리아어"

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr "일본어"

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr "한국어"

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr "네덜란드어"

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr "포르투갈어"

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr "러시아어"

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr "터키어"

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr "번체 중국어"
