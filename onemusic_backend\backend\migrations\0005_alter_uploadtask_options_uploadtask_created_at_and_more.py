# Generated by Django 5.1.11 on 2025-08-01 15:58

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('backend', '0004_music_partition'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='uploadtask',
            options={'ordering': ['-created_at']},
        ),
        migrations.AddField(
            model_name='uploadtask',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='uploadtask',
            name='format_type',
            field=models.CharField(default='mp3', max_length=10),
        ),
        migrations.AddField(
            model_name='uploadtask',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='uploadtask',
            name='webdav_config',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='upload_tasks', to='backend.webdavconfig'),
        ),
        migrations.AddField(
            model_name='webdavconfig',
            name='signature',
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
        migrations.AddIndex(
            model_name='uploadtask',
            index=models.Index(fields=['user', 'status'], name='backend_upl_user_id_83a764_idx'),
        ),
        migrations.AddIndex(
            model_name='uploadtask',
            index=models.Index(fields=['created_at'], name='backend_upl_created_6c98b4_idx'),
        ),
    ]
