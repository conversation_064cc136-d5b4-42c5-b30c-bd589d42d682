from django.contrib import admin
from django.utils import timezone

from .models import Product, Music, UserDownloadedMusic, Profile, UploadTask, RedemptionRecord, RedeemCode

admin.site.register(UserDownloadedMusic)
admin.site.register(UploadTask)
@admin.register(Music)
class MusicAdmin(admin.ModelAdmin):
    list_display = ('title', 'artist', 'album','could_download','created')
    search_fields = ['hash']

@admin.register(Product)
class MusicAdmin(admin.ModelAdmin):
    list_display = ('name','price','points')

@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = ('user',)

@admin.register(RedeemCode)
class RedeemCodeAdmin(admin.ModelAdmin):
    list_display = ('code', 'points', 'expiration_date', 'is_redeemed')
    actions = ['delete_expired_codes']

    def is_redeemed(self, obj):
        return obj.is_redeemed()
    is_redeemed.boolean = True

    def delete_expired_codes(self, request, queryset):
        queryset.filter(expiration_date__lt=timezone.now()).delete()
    delete_expired_codes.short_description = "鍒犻櫎鎵€鏈夎繃鏈熺殑鍏戞崲鐮�"

@admin.register(RedemptionRecord)
class RedemptionRecordAdmin(admin.ModelAdmin):
    list_display = ('user', 'redeem_code', 'redeemed_at')

