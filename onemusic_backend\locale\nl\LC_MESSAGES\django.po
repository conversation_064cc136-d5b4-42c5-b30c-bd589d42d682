# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "Muziek bestaat niet"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "Downloadverzoek voor lied mislukt"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "Het lied wordt gedownload"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "Downloadtaak aangemaakt"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "Downloadverzoek voor het lied ingediend, later beschikbaar"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "Ongeldige aanvraagmethode"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "Ongeldige sleutel"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "Song hash-waarde ontbreekt"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "Downloadstatus bijgewerkt"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "Het lied is klaar om te downloaden, geen update nodig"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "Lied niet gevonden"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "Downloadgeschiedenis niet gevonden"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "Downloadgeschiedenis succesvol verwijderd"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "Uploadtaak succesvol verwijderd"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "E-mail, wachtwoord en verificatiecode mogen niet leeg zijn"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "Verificatie van code mislukt, probeer opnieuw"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "Fout in e-mailheader, verzenden mislukt"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "Verificatie-e-mail verzonden, controleer je inbox."

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "Ongeldige of verlopen link"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "Onvolledige verificatiegegevens"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "E-mail of wachtwoord onjuist"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "Gebruikersconfiguratie niet gevonden"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "Maximaal 5 configuraties toegestaan"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "WebDAV-verbinding mislukt: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "Configuratie niet gevonden"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "Verplichte velden ontbreken"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "Downloadverzoek mislukt"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "Uploadverzoek mislukt"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "Uploadtaak aangemaakt"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "Ongeldige WebDAV-configuratie"

#: backend/webdav.py:145
msgid "未授权"
msgstr "Niet geautoriseerd"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "Taak niet gevonden"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "Vereenvoudigd Chinees"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr "Arabisch"

#: onemusic_backend/settings.py:133
msgid "German"
msgstr "Duits"

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "Engels"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr "Spaans"

#: onemusic_backend/settings.py:136
msgid "French"
msgstr "Frans"

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr "Hindi"

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr "Italiaans"

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr "Japans"

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr "Koreaans"

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr "Nederlands"

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr "Portugees"

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr "Russisch"

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr "Turks"

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr "Traditioneel Chinees"

