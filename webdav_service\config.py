import os

# WebDAV服务配置
WEBDAV_SECRET_KEY = '7pERYJmLTgimM499yB6ZYptKEFTmr3jBkoowFzP5Za'
WEBDAV_JWT_SECRET = '7pERYJmLTgimM499yB6ZYptKEFTmr3jBkoowFzP5Za'  # JWT密钥，与Django后端保持一致

# 后端服务配置
BACKEND_URL = os.getenv('BACKEND_URL', 'https://backend.1music.cc/')
DOWNLOAD_SERVER_URL = os.getenv('DOWNLOAD_SERVER_URL', 'https://oss.1music.cc')

# HTTP代理配置
HTTP_PROXY = os.getenv('HTTP_PROXY', 'http://**********:12799')

# Redis配置
REDIS_HOST = os.getenv('REDIS_HOST', '1Panel-redis-8DAl')
REDIS_PORT = int(os.getenv('REDIS_PORT', 6379))
REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', 'redis_rSDBA3')
REDIS_DB_CELERY = int(os.getenv('REDIS_DB_CELERY', 1))

# Celery配置
CELERY_BROKER_URL = f'redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB_CELERY}'
CELERY_RESULT_BACKEND = f'redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB_CELERY}'

# 临时目录配置
TEMP_DIR = os.getenv('TEMP_DIR', './temp')

# 音频转码配置
SUPPORTED_FORMATS = ['mp3', 'flac', 'wav', 'aac', 'm4a', 'ogg']
DEFAULT_AUDIO_QUALITY = '320k'  # 默认音频质量

# WebDAV上传配置
WEBDAV_TIMEOUT = 300  # WebDAV上传超时时间（秒）
MAX_RETRY_ATTEMPTS = 3  # 最大重试次数

# 日志配置
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
