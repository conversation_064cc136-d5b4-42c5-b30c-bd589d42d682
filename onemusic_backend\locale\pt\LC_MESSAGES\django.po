# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "A música não existe"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "Falha ao solicitar o download da música"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "A música está sendo baixada"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "Tarefa de download criada"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "Solicitação de download da música enviada, disponível mais tarde"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "Método de solicitação inválido"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "Chave inválida"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "Valor hash da música ausente"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "Status do download atualizado"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "A música está pronta para download, nenhuma atualização necessária"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "Música não encontrada"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "Registro de download não encontrado"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "Registro de download excluído com sucesso"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "Tarefa de upload excluída com sucesso"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "O e-mail, senha e código de verificação não podem estar vazios"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "Falha na verificação do código, tente novamente"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "Erro no cabeçalho do e-mail, falha ao enviar"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "E-mail de verificação enviado, verifique sua caixa de entrada."

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "Link inválido ou expirado"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "Dados de verificação incompletos"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "E-mail ou senha incorretos"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "Configuração do usuário não encontrada"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "Máximo de 5 configurações permitidas"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "Falha na conexão WebDAV: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "Configuração não encontrada"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "Campos obrigatórios ausentes"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "Falha na solicitação de download"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "Falha na solicitação de upload"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "Tarefa de upload criada"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "Configuração WebDAV inválida"

#: backend/webdav.py:145
msgid "未授权"
msgstr "Não autorizado"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "Tarefa não encontrada"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "Chinês Simplificado"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr "Árabe"

#: onemusic_backend/settings.py:133
msgid "German"
msgstr "Alemão"

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "Inglês"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr "Espanhol"

#: onemusic_backend/settings.py:136
msgid "French"
msgstr "Francês"

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr "Hindi"

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr "Italiano"

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr "Japonês"

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr "Coreano"

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr "Holandês"

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr "Português"

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr "Russo"

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr "Turco"

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr "Chinês Tradicional"