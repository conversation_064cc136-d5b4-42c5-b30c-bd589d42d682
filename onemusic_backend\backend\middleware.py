# middleware.py
from django.utils import translation
from django.utils.deprecation import MiddlewareMixin

class LocaleMiddleware(MiddlewareMixin):
    def process_request(self, request):
        locale = request.COOKIES.get('NEXT_LOCALE')
        if locale:
            # 替换 - 为 _
            # 设置当前请求的语言
            translation.activate(locale)
            request.LANGUAGE_CODE = translation.get_language()
        else:
            # 默认语言
            translation.activate('zh-CN')  # 可以改成你的默认语言
            request.LANGUAGE_CODE = 'zh-CN'
