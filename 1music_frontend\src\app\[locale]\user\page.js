'use client'

import React, {createContext, useLayoutEffect, useState} from 'react';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import {styled, ThemeProvider} from '@mui/material/styles';
import ProfilePage from "@/src/app/[locale]/user/profile";
import SongsPage from "@/src/app/[locale]/user/songs";
import {backendUrl} from "@/src/app/[locale]/config";
import NoSsr from "@mui/material/NoSsr";
import {NotificationsProvider} from "@toolpad/core";
import UserUploadTasks from "@/src/app/[locale]/user/uploadTask";
import {createTheme, CssBaseline} from "@mui/material";
import {useTranslations} from 'next-intl';
import {useRouter} from "next/navigation";

export const GlobalContext = createContext({});

const StyledAppBar = styled(AppBar)(({ theme }) => ({
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.primary,
    boxShadow: 'none',
    borderBottom: `1px solid ${theme.palette.divider}`,
}));

const theme = createTheme({
    colorSchemes: {
        dark: true,
    },
});

const Home = () => {
    const t = useTranslations('User');
    const router = useRouter();
    const [tabValue, setTabValue] = useState(0);
    const [userData, setUserdata] = useState(null)
    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const fetchProfile = async () => {
        try {
            const response = await fetch(backendUrl + 'get_user_profile/', {
                method: 'GET',
                credentials: 'include',
            });

            if (response.redirected) {
                await router.push('/login');
                return;
            }

            if (!response.ok) {
                throw new Error('Failed to fetch');
            }

            const data = await response.json();
            setUserdata(data);
        } catch (error) {
            await router.push('/login');
        }
    };

    useLayoutEffect(() => {
        fetchProfile()
    }, []);

    return (
        <NoSsr>

            <NotificationsProvider>
                <ThemeProvider theme={theme} noSsr><CssBaseline />
                    <StyledAppBar position="static">
                        <Toolbar>
                            <Typography onClick={() => {router.push('/')}} variant="h6" component="div" sx={{
                                flexGrow: 0.1,
                                fontFamily: "HarmonyOS Sans,system-ui",
                                letterSpacing:-0.5,
                                fontWeight: 'bold',
                            }}>
                                1Music.cc
                            </Typography>
                            <Box sx={{ mx: 2 ,mr:0.5, flexGrow:1, overflow: 'hidden'}}>
                                <Tabs value={tabValue} variant='scrollable' scrollButtons onChange={handleTabChange} indicatorColor="primary" textColor="primary">
                                    <Tab label={t('profile')} />
                                    <Tab label={t('downloaded')} />
                                    <Tab label={t('uploadTasks')} />
                                </Tabs>
                            </Box>
                        </Toolbar>
                    </StyledAppBar>
                    <GlobalContext.Provider value={{
                        webdavConfigs: userData?.webdav_configs || [],
                        webdavJwt: userData?.webdav_jwt
                    }}>
                        {(tabValue === 0 && userData) && <ProfilePage userData={userData} />}
                        {(tabValue === 1 && userData) && <SongsPage />}
                        {tabValue === 2 && <UserUploadTasks />}
                    </GlobalContext.Provider>
                </ThemeProvider>
            </NotificationsProvider>
        </NoSsr>
    );
};

export default Home;
