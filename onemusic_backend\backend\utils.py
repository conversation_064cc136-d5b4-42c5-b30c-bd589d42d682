import hashlib

import jwt
import requests
from requests import RequestException, Timeout
from onemusic_backend import settings
from datetime import datetime,timedelta
import random

from onemusic_backend.settings import DOWNLOAD_SERVER_URL, DOWNLOADER_SECRET
from .models import Music

def generate_jwt(payload):
    """生成 JWT 令牌"""
    current_time = datetime.utcnow()
    hour_floor = current_time.replace(minute=0, second=0, microsecond=0)
    expiration = hour_floor + timedelta(hours=2)
    payload.update({"exp": expiration})
    token = jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.JWT_CONFIG['ALGORITHM'])
    return token

def generate_source_jwt(payload):
    """生成 JWT 令牌，有效期为当天0点起加2天"""
    # 获取当前 UTC 时间
    current_time = datetime.utcnow()

    # 获取当天的 0 点时间（UTC）
    day_start = current_time.replace(hour=0, minute=0, second=0, microsecond=0)

    # 设置过期时间为当天 0 点 + 2 天
    expiration = day_start + timedelta(days=2)

    # 更新 payload 并生成 token
    payload.update({"exp": expiration})
    token = jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.JWT_CONFIG['ALGORITHM'])

    return token

def verify_jwt(token):
    """验证 JWT 令牌"""
    try:
        decoded = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.JWT_CONFIG['ALGORITHM']])
        return decoded
    except jwt.ExpiredSignatureError:
        return None  # 令牌过期
    except jwt.InvalidTokenError:
        return None  # 令牌无效

proxies = {
    'http': 'http://1music:%WZp0O7453hj23hfwn2Cs@9XHh@150.230.126.25:60101',
    'https': 'http://1music:%WZp0O7453hj23hfwn2Cs@9XHh@150.230.126.25:60101'
}

def test_webdav_connection(url, username, password):
    """
    Tests a WebDAV connection by sending a basic request to the provided URL.

    Args:
        url (str): The WebDAV server URL.
        username (str): The username for authentication.
        password (str): The password for authentication.

    Returns:
        tuple: (bool, str) where the first value indicates success and the second contains an error message if any.
    """
    try:
        response = requests.request(
            method="PROPFIND",
            url=url,
            auth=(username, password),
            timeout=10,
            stream=True,
            headers={"Depth": "1"},
            proxies=proxies
        )
        status_code = response.status_code
        response.close() 
        if status_code in (200, 207):
            return True, ""
        elif status_code == 401:
            return False, "Authentication failed. Check username and password."
        elif status_code == 403:
            return False, "Access forbidden. Check your permissions."
        else:
            return False, f"Unexpected HTTP status code: {status_code}."

    except Timeout:
        return False, "The request timed out. The server may be unreachable or slow."
    except ConnectionError:
        return False, "Failed to connect to the WebDAV server. Check the URL and your network connection."
    except RequestException as e:
        return False, f"An error occurred while testing the WebDAV connection: {str(e)}"
    except Exception as e:
        return False, f"An unexpected error occurred: {str(e)}"

def generate_random_md5_like_string():
    """ 生成一个 32 位的随机十六进制字符串（模拟 MD5） """
    return ''.join(random.choices('0123456789abcdef', k=32))

def generate_secure_hash_salt(partition, song_hash, format_type):
    """
    生成安全的hash_salt，用于链接验证
    参数: partition(分区), song_hash(歌曲hash), format_type(格式)
    返回: 加盐后的hash值
    """
    # 将参数组合并加上密钥进行hash
    combined_string = f"{partition}_{song_hash}_{format_type}_{DOWNLOADER_SECRET}"
    return hashlib.md5(combined_string.encode('utf-8')).hexdigest()


def get_random_music():
    music_set = set()  # 用于去重
    music_list = []

    for _ in range(15):  # 进行 30 次查询
        random_start = generate_random_md5_like_string()

        # 查询大于等于该字符串的最小 1 条数据
        greater_result = Music.objects.filter(hash__gte=random_start).order_by('hash').values('hash', 'title', 'artist',
                                                                                              'album', 'partition')[:1]
        # 查询小于等于该字符串的最大 1 条数据
        smaller_result = Music.objects.filter(hash__lte=random_start).order_by('-hash').values('hash', 'title',
                                                                                               'artist', 'album', 'partition')[:1]
        # 去重并添加到列表
        for result in greater_result:
            if result['hash'] not in music_set:
                music_set.add(result['hash'])
                result['videoId'] = 'id'
                result['song_hash'] = hashlib.sha256(
            (result['title'] + result['album'] + result['artist'] + result[
                'videoId'] + DOWNLOADER_SECRET).encode('utf-8')
        ).hexdigest()
                # 生成安全的hash_salt并使用新的缩略图链接格式
                pic_hash_salt = generate_secure_hash_salt(result['partition'], result['hash'], 'webp')
                result['thumbnail'] = f"{DOWNLOAD_SERVER_URL}/{result['partition']}/{result['hash']}/webp?salt={pic_hash_salt}"
                music_list.append(result)

        for result in smaller_result:
            if result['hash'] not in music_set:
                music_set.add(result['hash'])
                result['videoId'] = 'id'
                result['song_hash'] = hashlib.sha256(
                    (result['title'] + result['album'] + result['artist'] + result[
                        'videoId'] + DOWNLOADER_SECRET).encode('utf-8')
                ).hexdigest()
                # 生成安全的hash_salt并使用新的缩略图链接格式
                pic_hash_salt = generate_secure_hash_salt(result['partition'], result['hash'], 'webp')
                result['thumbnail'] = f"{DOWNLOAD_SERVER_URL}/{result['partition']}/{result['hash']}/webp?salt={pic_hash_salt}"
                music_list.append(result)

    return music_list






