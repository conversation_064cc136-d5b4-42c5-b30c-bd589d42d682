# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n % 10 == 1 && n % 100 != 11 ? 0 : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2);\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "Музыка не существует"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "Не удалось запросить загрузку песни"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "Песня загружается"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "Задача загрузки создана"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "Запрос на загрузку песни отправлен, будет доступен позже"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "Недопустимый метод запроса"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "Недопустимый ключ"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "Отсутствует хеш песни"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "Статус загрузки обновлен"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "Песня готова к загрузке, повторное обновление не требуется"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "Песня не найдена"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "Запись загрузки не найдена"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "Запись загрузки успешно удалена"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "Задача загрузки успешно удалена"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "Email, пароль и код подтверждения не могут быть пустыми"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "Ошибка проверки кода подтверждения, попробуйте снова"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "Ошибка заголовка письма, отправка не удалась"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "Письмо с подтверждением отправлено, проверьте почтовый ящик."

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "Ссылка недействительна или устарела"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "Данные для проверки неполные"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "Неправильный email или пароль"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "Конфигурация пользователя не найдена"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "Разрешено не более 5 конфигураций"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "Ошибка подключения к WebDAV: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "Конфигурация не найдена"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "Отсутствуют необходимые поля"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "Ошибка запроса на загрузку"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "Ошибка запроса на загрузку файла"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "Задача загрузки создана"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "Недопустимая конфигурация WebDAV"

#: backend/webdav.py:145
msgid "未授权"
msgstr "Неавторизован"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "Задача не найдена"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "Упрощенный китайский"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr "Арабский"

#: onemusic_backend/settings.py:133
msgid "German"
msgstr "Немецкий"

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "Английский"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr "Испанский"

#: onemusic_backend/settings.py:136
msgid "French"
msgstr "Французский"

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr "Хинди"

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr "Итальянский"

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr "Японский"

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr "Корейский"

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr "Нидерландский"

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr "Португальский"

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr "Русский"

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr "Турецкий"

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr "Традиционный китайский"

