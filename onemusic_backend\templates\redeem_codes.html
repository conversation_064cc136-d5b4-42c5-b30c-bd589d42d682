{% extends "admin/base.html" %}
{% block content %}
<h2>兑换码管理</h2>

<!-- 生成新兑换码 -->
<form method="POST">
    {% csrf_token %}
    <label for="points">积分:</label>
    <input type="number" name="points" required>
    <button type="submit">生成兑换码</button>
</form>

<!-- 兑换码列表 -->
<table border="1">
    <tr>
        <th>兑换码</th>
        <th>积分</th>
        <th>过期时间</th>
        <th>状态</th>
        <th>操作</th>
    </tr>
    {% for code in redeem_codes %}
    <tr>
        <td>{{ code.code }}</td>
        <td>{{ code.points }}</td>
        <td>{{ code.expiration_date }}</td>
        <td>
            {% if code.is_expired %}已过期
            {% elif code.is_redeemed %}已兑换
            {% else %}可用
            {% endif %}
        </td>
        <td>
            <form method="POST" action="{% url 'delete_code' code.id %}">
                {% csrf_token %}
                <button type="submit">删除</button>
            </form>
        </td>
    </tr>
    {% endfor %}
</table>

{% endblock %}
