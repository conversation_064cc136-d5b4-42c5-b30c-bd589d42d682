# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "音乐不存在"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "请求歌曲下载失败"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "歌曲正在下载中"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "下载任务已创建"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "歌曲下载请求已提交，稍后可用"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "无效的请求方法"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "无效的密钥"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "缺少歌曲哈希值"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "下载状态已更新"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "歌曲已准备好下载，无需重复更新"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "歌曲未找到"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "下载记录未找到"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "下载记录删除成功"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "上传任务删除成功"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "邮箱、密码和验证码不能为空"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "验证码验证失败，请重试"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "邮件头错误，发送失败"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "验证邮件已发送，请查收。"

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "链接无效或已过期"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "验证数据不完整"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "邮箱或密码错误"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "用户配置未找到"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "最多允许5个配置"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "WebDAV 连接失败: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "配置未找到"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "缺少必要字段"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "请求下载失败"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "请求上传失败"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "上传任务已创建"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "无效的 WebDAV 配置"

#: backend/webdav.py:145
msgid "未授权"
msgstr "未授权"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "任务未找到"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "简体中文"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr "阿拉伯语"

#: onemusic_backend/settings.py:133
msgid "German"
msgstr "德语"

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "英语"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr "西班牙语"

#: onemusic_backend/settings.py:136
msgid "French"
msgstr "法语"

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr "印地语"

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr "意大利语"

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr "日语"

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr "韩语"

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr "荷兰语"

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr "葡萄牙语"

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr "俄语"

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr "土耳其语"

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr "繁体中文"
