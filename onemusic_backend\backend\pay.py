import hashlib
import json
import uuid

import requests
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.translation import gettext as _
from backend.models import Order, Product
from onemusic_backend.settings import MERCHANT_ID, PAYMENT_URL, PAYMENT_KEY


def generate_sign(params, key):
    sorted_params = "&".join(f"{k}={v}" for k, v in sorted(params.items()) if v and k not in ["sign", "sign_type"])
    sign_string = f"{sorted_params}{key}"
    return hashlib.md5(sign_string.encode('utf-8')).hexdigest()


@require_http_methods(["GET"])
def list_products(request):
    products = Product.objects.all()
    data = [{
        'id': product.id,
        'name': product.name,
        'price': str(product.price),  # Convert Decimal to string for JSON
        'points': product.points
    } for product in products]

    return JsonResponse({'products': data})

@csrf_exempt
@login_required
def create_order(request):
    if request.method != 'POST':
        return JsonResponse({'code': 0, 'msg': 'Invalid request method'})

    data = json.loads(request.body)
    user = request.user
    product = get_object_or_404(Product, id=data.get('product_id'))
    out_trade_no = uuid.uuid4()

    order = Order.objects.create(user=user, product=product, out_trade_no=out_trade_no, amount=product.price)

    payment_data = {
        "pid": MERCHANT_ID,
        "type": "alipay",
        "out_trade_no": order.out_trade_no,
        "notify_url": "https://1music.cc/backend/pay_notify/",
        "return_url": "https://1music.cc/user",
        "name": product.name,
        "money": str(product.price),
        "clientip": request.META.get('REMOTE_ADDR', '127.0.0.1'),
        "device": "pc"
    }
    payment_data['sign'] = generate_sign(payment_data, PAYMENT_KEY)

    response = requests.post(PAYMENT_URL, data=payment_data).json()

    if response['code'] == 1:
        return JsonResponse({'code': 1, 'payurl': response.get('payurl')})
    else:
        order.status = 'failed'
        order.save()
        return JsonResponse({'code': 0, 'msg': 'Payment initiation failed'})


@csrf_exempt
def payment_notify(request):
    data = request.GET.dict()
    sign = data.pop("sign", "")

    if generate_sign(data, PAYMENT_KEY) != sign:
        return JsonResponse({'code': 0, 'msg': 'Invalid signature'})

    if data.get("trade_status") == "TRADE_SUCCESS":
        order = get_object_or_404(Order, out_trade_no=data["out_trade_no"])
        if order.status != 'success':
            order.status = 'success'
            order.trade_no = data["trade_no"]
            order.save()
            order.user.profile.points += order.product.points  # 璐�拱鍟嗗搧鍚庡�鍔犲�搴旂殑绉�垎
            order.user.profile.save()
        return JsonResponse({'code': 1, 'msg': 'success'})

    return JsonResponse({'code': 0, 'msg': 'Payment failed'})