import os
import requests
import hashlib
from flask import Flask, request, jsonify, send_file, abort
from flask_cors import CORS
from celery import Celery
from utils import download_audio, generate_secure_hash_salt, rclone_copy_from_remote
from config import (
    DOWNLOAD_KEY, BACKEND_URL, get_remote_path, get_temp_download_dir, get_temp_upload_dir,
    TEMP_FILE_MAX_AGE_SECONDS, DOWNLOAD_DIR_MAX_AGE_SECONDS
)

app = Flask(__name__)
CORS(app)

DOWNLOAD_QUEUE = 'download_queue'
# 配置 Celery
config = {}
config['broker_url'] = 'redis://:redis_b5iJDa@1Panel-redis-6Fqj:6379/0'
config['result_backend'] = 'redis://:redis_b5iJDa@1Panel-redis-6Fqj:6379/0'
celery = Celery('app', broker=config['broker_url'])
celery.conf.update(config)
celery.conf.update({
    'task_routes': {
        'download_file': {'queue': DOWNLOAD_QUEUE}
    }
})

# 确保临时目录存在
get_temp_upload_dir()
get_temp_download_dir()

@celery.task(bind=True,queue=DOWNLOAD_QUEUE)
def download_file(self, music_hash, title, artist, album, video_id, partition=0):
    """优化的下载文件 Celery 任务"""
    task_id = f"download_{music_hash}"
    self.request.id = task_id

    try:
        # 使用优化的下载函数
        download_audio(
            music_hash=music_hash,
            title=title,
            artist=artist,
            album=album,
            video_id=video_id,
            partition=partition
        )

        # 下载完成后，文件已经复制到临时下载文件夹，可以立即触发更新回调
        # 这样用户可以立即访问文件，而不需要等待rclone上传完成
        callback_data = {
            "secret": DOWNLOAD_KEY,
            "hash": music_hash
        }

        try:
            response = requests.post(BACKEND_URL + 'update_download_status/', json=callback_data, timeout=10)
            if response.status_code == 200:
                print(f"下载完成回调成功: {music_hash}")
            else:
                print(f"下载完成回调失败: {music_hash}, status: {response.status_code}")
        except Exception as callback_error:
            print(f"下载完成回调异常: {music_hash}, error: {str(callback_error)}")
            # 回调失败不影响下载流程

        # 启动后台上传任务，将文件移动到远端存储
        try:
            upload_to_remote_task.apply_async(args=[music_hash, partition], countdown=5)
            print(f"后台上传任务已启动: {music_hash}")
        except Exception as upload_error:
            print(f"启动后台上传任务失败: {music_hash}, error: {str(upload_error)}")

        print(f"下载和处理完成: {music_hash}")
        return

    except Exception as exc:
        print(f"下载失败: {str(exc)}")
        raise exc

# WebDAV功能已移除 - 将在独立项目中实现

@celery.task(queue=DOWNLOAD_QUEUE)
def cleanup_temp_files_task():
    """定期清理临时下载文件"""
    import glob
    import time

    temp_download_dir = get_temp_download_dir()
    temp_upload_dir = get_temp_upload_dir()

    # 清理临时上传文件（超过1小时的）
    for file_path in glob.glob(f"{temp_upload_dir}/*"):
        if os.path.isfile(file_path):
            try:
                file_age = time.time() - os.path.getmtime(file_path)
                if file_age > TEMP_FILE_MAX_AGE_SECONDS:
                    os.remove(file_path)
                    print(f"Cleaned up old upload temp file: {file_path}")
            except Exception as e:
                print(f"Failed to cleanup upload temp file {file_path}: {str(e)}")

    # 清理临时下载文件（超过2小时的，保留更长时间用于缓存）
    for file_path in glob.glob(f"{temp_download_dir}/*"):
        if os.path.isfile(file_path):
            try:
                file_age = time.time() - os.path.getmtime(file_path)
                if file_age > DOWNLOAD_DIR_MAX_AGE_SECONDS:
                    os.remove(file_path)
                    print(f"Cleaned up old download temp file: {file_path}")
            except Exception as e:
                print(f"Failed to cleanup download temp file {file_path}: {str(e)}")

    print("Temp files cleanup completed")


@celery.task(queue=DOWNLOAD_QUEUE)
def upload_to_remote_task(music_hash, partition=0):
    """
    后台任务：将临时上传文件夹中的文件移动到远端存储
    这个任务在下载完成和回调触发后异步执行
    """
    try:
        from utils import upload_files_to_remote, get_remote_path, get_temp_upload_dir

        upload_dir = get_temp_upload_dir()
        remote_path = get_remote_path(partition)

        # 检查文件是否还在临时上传文件夹中
        files_exist = any(
            os.path.exists(os.path.join(upload_dir, f"{music_hash}.{ext}"))
            for ext in ['webm', 'webp', 'jpg']
        )

        if not files_exist:
            print(f"No files found in upload directory for {music_hash}, skipping remote upload")
            return

        # 上传到远端
        upload_files_to_remote(music_hash, upload_dir, remote_path)
        print(f"Background upload to remote completed for {music_hash}")

    except Exception as e:
        print(f"Background upload to remote failed for {music_hash}: {str(e)}")
        # 后台上传失败不影响用户体验，因为文件已经在临时下载文件夹中可用


@app.route('/download', methods=['POST'])
def download():
    """统一的下载接口（优化版）"""
    if request.json.get("secret") != DOWNLOAD_KEY:
        return jsonify({"error": "Unauthorized"}), 401

    # 获取请求参数
    data = request.json
    required_fields = ['music_hash', 'title', 'artist', 'album', 'video_id']

    # 验证必需字段
    for field in required_fields:
        if not data.get(field):
            return jsonify({"error": f"Missing required field: {field}"}), 400

    music_hash = data['music_hash']
    title = data['title']
    artist = data['artist']
    album = data['album']
    video_id = data['video_id']
    partition = data.get('partition', 0)  # 默认分区为0

    # 验证分区号
    if not isinstance(partition, int) or partition < 0:
        return jsonify({"error": "Invalid partition number"}), 400

    # 分区验证（不需要创建本地目录）

    # 提交下载任务到 Celery
    download_file.apply_async(
        (music_hash, title, artist, album, video_id, partition),
        task_id=f"download_{music_hash}",
        queue=DOWNLOAD_QUEUE
    )

    return jsonify({"details": "Download task submitted", "partition": partition}), 200


# 转码接口已移除 - 现在由前端处理转码

# WebDAV上传接口已移除 - 将在独立项目中实现

@app.route('/<int:partition>/<music_hash>/<format_type>', methods=['GET'])
def get_file(partition, music_hash, format_type):
    """
    新的统一文件服务接口
    URL格式: /[分区]/[音乐hash]/[格式]?salt=[hash_salt]
    支持格式: webm(音频), webp(图片)
    """
    # 从查询字符串获取hash_salt
    hash_salt = request.args.get('salt')
    if not hash_salt:
        abort(400, description="Missing salt parameter")

    # 验证hash_salt
    expected_salt = generate_secure_hash_salt(partition, music_hash, format_type)
    if hash_salt != expected_salt:
        abort(403, description="Access denied")

    # 验证格式
    if format_type not in ['webm', 'webp']:
        abort(400, description="Unsupported format")

    # 获取临时下载目录
    temp_download_dir = get_temp_download_dir()

    # 生成文件名
    filename = f"{music_hash}.{format_type}"
    local_file_path = os.path.join(temp_download_dir, filename)

    # 步骤1: 首先检查临时下载文件夹是否已有文件（优化后的流程）
    if os.path.exists(local_file_path):
        print(f"File found in temp download directory: {filename}")
        # 设置正确的MIME类型并直接返回
        mimetype = get_mimetype_for_format(format_type)
        return send_file(local_file_path, mimetype=mimetype, as_attachment=False)

    # 步骤2: 如果临时下载文件夹没有，尝试从远端下载
    remote_path = get_remote_path(partition)
    success, local_file_path, _ = rclone_copy_from_remote(remote_path, filename, temp_download_dir)

    if not success:
        # 对于图片，尝试jpg格式作为备选
        if format_type == 'webp':
            jpg_filename = f"{music_hash}.jpg"
            jpg_local_path = os.path.join(temp_download_dir, jpg_filename)

            # 先检查临时下载文件夹是否有jpg文件
            if os.path.exists(jpg_local_path):
                print(f"JPG file found in temp download directory: {jpg_filename}")
                return send_file(jpg_local_path, mimetype='image/jpeg', as_attachment=False)

            # 如果没有，尝试从远端下载jpg
            success, local_file_path, _ = rclone_copy_from_remote(remote_path, jpg_filename, temp_download_dir)
            if success:
                return send_file(local_file_path, mimetype='image/jpeg', as_attachment=False)

        abort(404, description="File not found")

    # 设置正确的MIME类型并返回文件
    mimetype = get_mimetype_for_format(format_type)
    return send_file(local_file_path, mimetype=mimetype, as_attachment=False)


def get_mimetype_for_format(format_type):
    """
    根据文件格式返回对应的MIME类型
    """
    mime_mapping = {
        'webm': 'audio/webm',
        'webp': 'image/webp',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg'
    }
    return mime_mapping.get(format_type, 'application/octet-stream')

# JWT下载接口已移除 - 现在使用新的hash_salt验证方式

if __name__ == '__main__':
    app.run(debug=True)