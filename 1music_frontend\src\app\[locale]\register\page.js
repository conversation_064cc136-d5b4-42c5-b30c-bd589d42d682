'use client'

import React, { useState } from 'react';
import axios from 'axios';
import { <PERSON>Field, Button, Dialog, DialogTitle, DialogContent, DialogActions, Alert } from '@mui/material';
import { makeStyles } from '@mui/styles';
import {backendUrl} from "@/src/app/[locale]/config";
import NoSsr from '@mui/material/NoSsr';
import { Turnstile } from '@marsidev/react-turnstile';
import Typography from "@mui/material/Typography";
import {useTranslations} from "next-intl";
import SEO from "@/src/app/[locale]/component/Seo";

const useStyles = makeStyles(() => ({
    linearGradientButton: {
        position: 'relative',
        overflow: 'hidden',
        fontWeight: 'bold',
        borderRadius: '4px',
        '& > span': {
            position: 'relative',
            zIndex: 1,
            color: 'white',
            fontSize: '16px',
            fontFamily: 'Roboto, Arial, sans-serif',
            fontWeight: 'bold',
        },
        '&::before': {
            content: '""',
            position: 'absolute',
            inset: '0',
            background: 'linear-gradient(135deg, #6253e1, #04befe)',
            zIndex: 0,
            borderRadius: 'inherit',
            transition: 'opacity 0.3s',
            opacity: 1,
        },
        '&:hover::before': {
            opacity: 0,
        },
    },
}));

const Register = () => {
    const classes = useStyles();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [dialogOpen, setDialogOpen] = useState(false);
    const [dialogMessage, setDialogMessage] = useState('');
    const [alertSeverity, setAlertSeverity] = useState('info');
    const [isVerificationSent, setIsVerificationSent] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const [captchaToken, setCaptchaToken] = useState(null);
    const t = useTranslations('Register')

    const isEmailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    const isPasswordValid = password.length >= 10 && password.length <= 50;
    const isFormValid = isEmailValid && isPasswordValid && password === confirmPassword;
    const isHumanVerified = captchaToken !== null;

    const handleSendVerificationLink = async () => {
        setIsProcessing(true);
        try {
            const response = await axios.post( backendUrl+'send_verification_email/', {
                email: email,
                password: password,
                captchaToken: captchaToken
            }, {
                withCredentials: true
            });
            setAlertSeverity('info');
            setDialogMessage(response.data.detail || t("verificationSent"));
            setIsVerificationSent(true);
        } catch (error) {
            setAlertSeverity('error');
            setDialogMessage(error.response?.data?.detail || t("verificationFailed"));
        } finally {
            setIsProcessing(false);
            setDialogOpen(true);
        }
    };

    const handleLogin = async () => {
        try {
            const response = await axios.post( backendUrl + 'login/', {
                email,
                password,
            }, {
                withCredentials: true
            });

            if (response.data.detail === '登录成功') {
                window.location.href = '/'
            } else {
                setAlertSeverity('error');
            }
            setDialogMessage(t("loginSuccess"));
        } catch (error) {
            setAlertSeverity('error');
            setDialogMessage(error.response?.data?.detail || t("unknownError"));
        } finally {
            setDialogOpen(true);
        }
    };

    const handleDialogClose = () => {
        setDialogOpen(false);
    };

    return (
        <NoSsr>
            <SEO />
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100vh', padding: '0 20px' }}>
                <Typography variant="h6" component="div" sx={{
                    fontFamily: "HarmonyOS Sans,system-ui",
                    letterSpacing:-0.5,
                    fontWeight: 'bold',
                    fontSize:'30px',
                    textAlign: 'center',
                    paddingBottom:'30px'
                }}>
                    1Music.cc
                </Typography>
                <div style={{ width: '100%', maxWidth: '400px', display: 'flex', flexDirection: 'column', gap: '20px' }}>
                    <TextField
                        label={t("email")}
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        error={!isEmailValid && email.length > 0}
                        helperText={!isEmailValid && email.length > 0 ? t("invalidEmail") : ''}
                        fullWidth
                        required
                    />
                    <TextField
                        label={t("password")}
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        error={!isPasswordValid && password.length > 0}
                        helperText={!isPasswordValid && password.length > 0 ? t("passwordLength") : ''}
                        fullWidth
                        required
                    />
                    <TextField
                        label={t("confirmPassword")}
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        error={password !== confirmPassword && confirmPassword.length > 0}
                        helperText={password !== confirmPassword && confirmPassword.length > 0 ? t("passwordMismatch") : ''}
                        fullWidth
                        required
                    />
                    {!isVerificationSent ? (
                        <>
                            <Turnstile siteKey="0x4AAAAAAA8xnyzftf2JTc3Y" onSuccess={setCaptchaToken} />
                            <Button
                                variant="contained"
                                onClick={handleSendVerificationLink}
                                className={classes.linearGradientButton}
                                disabled={isProcessing || !isFormValid || !isHumanVerified}
                            >
                                <span>{isProcessing ? t("sending") : t("sendVerification")}</span>
                            </Button>
                        </>
                    ) : (
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                            <Button
                                variant="contained"
                                onClick={handleLogin}
                                className={classes.linearGradientButton}
                                disabled={isProcessing || !isEmailValid || !isPasswordValid}
                            >
                                <span>{isProcessing ? t("loggingIn") : t("verifiedLogin")}</span>
                            </Button>
                            <Button
                                variant="outlined"
                                onClick={() => {setIsVerificationSent(false);setCaptchaToken(null)}}
                                disabled={isProcessing}
                            >
                                <span>{t("sendAgain")}</span>
                            </Button>
                        </div>
                    )}
                </div>
            </div>
            <Dialog open={dialogOpen} onClose={handleDialogClose}>
                <DialogTitle>{alertSeverity === 'error' ? t("error") : t('tip')}</DialogTitle>
                <DialogContent>
                    <Alert severity={alertSeverity}>{dialogMessage}</Alert>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleDialogClose} color="primary">{t("confirm")}</Button>
                </DialogActions>
            </Dialog>
        </NoSsr>
    );
};

export default Register;
