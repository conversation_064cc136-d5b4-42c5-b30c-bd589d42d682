import React, { useState } from "react";
import {
    <PERSON>ton,
    <PERSON>alog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Select,
    MenuItem,
} from "@mui/material";
import axios from "axios";
import { useTranslations } from "next-intl";
import { backendUrl } from "@/src/app/[locale]/config";

const RechargeDialog = () => {
    const t = useTranslations("RechargeDialog");
    const [open, setOpen] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState("");
    const [loading, setLoading] = useState(false);
    const [payUrl, setPayUrl] = useState("");
    const [products, setProducts] = useState([]);
    const [error, setError] = useState('');

    const fetchProducts = async () => {
        try {
            const response = await axios.get(`${backendUrl}products/`);
            setProducts(response.data.products);
        } catch (error) {
            console.log(t("fetchError"));
        }
    };

    const handleClickOpen = () => {
        setOpen(true);
        fetchProducts();
    };

    const handleRecharge = async () => {
        setLoading(true);
        try {
            const response = await axios.post(backendUrl + "create_order/", {
                product_id: selectedProduct,
            }, { withCredentials: true });
            if (response.data.code === 1) {
                window.location.href = response.data.payurl;
            } else {
                alert(t("paymentInitError"));
            }
        } catch (error) {
            alert(t("requestFailed"));
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        setOpen(false);
        setPayUrl("");
        setLoading(false);
        setError('');
    };

    return (
        <div>
            <Button sx={{ mt: 2 }} variant="contained" size='small' onClick={handleClickOpen}>
                {t("supportAuthor")}
            </Button>
            <Dialog open={open} onClose={handleClose}>
                <DialogTitle>{t("supportAuthor")}</DialogTitle>
                <DialogContent>
                    <Select
                        fullWidth
                        value={selectedProduct}
                        onChange={(e) => setSelectedProduct(e.target.value)}
                        displayEmpty
                    >
                        <MenuItem value="" disabled>
                            {t("anyAmount")}
                        </MenuItem>
                        {products.map((product) => (
                            <MenuItem key={product.id} value={product.id}>
                                {product.name} - ¥{product.price}
                            </MenuItem>
                        ))}
                    </Select>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose} color="secondary">
                        {t("cancel")}
                    </Button>
                    <Button onClick={handleRecharge} color="primary" disabled={!selectedProduct || loading}>
                        {loading ? t("processing") : t("confirmPayment")}
                    </Button>
                </DialogActions>
            </Dialog>
        </div>
    );
};

export default RechargeDialog;