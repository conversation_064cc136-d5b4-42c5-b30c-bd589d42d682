import {AppRouterCacheProvider} from "@mui/material-nextjs/v15-appRouter";
import {notFound} from "next/navigation";
import {getMessages} from "next-intl/server";
import {routing} from "@/src/i18n/routing";
import {NextIntlClientProvider} from "next-intl";
import SEO from "@/src/app/[locale]/component/Seo";

export default async function RootLayout({children, params}) {
    const {locale} = await params;
    if (!routing.locales.includes(locale)) {
        notFound();
    }
    const messages = await getMessages();
    return (
        <html lang={locale}>
        <NextIntlClientProvider messages={messages}>
        <SEO />
        <body>
            <AppRouterCacheProvider>
                {children}
            </AppRouterCacheProvider>
        </body>
        </NextIntlClientProvider>
        </html>
    );
}
