import os
import hashlib
import subprocess
import time

from yt_dlp import YoutubeD<PERSON>

from config import (
    TEMP_DIR, COOKIES_FILE, BACKEND_URL, DOWNLOAD_KEY,
    RCLONE_COMMAND, RCLONE_CONFIG_FILE, PARTITION_REMOTE_MAPPING,
    get_remote_path, get_temp_upload_dir, get_temp_download_dir
)

def generate_secure_hash_salt(partition, music_hash, format_type):
    """
    生成安全的hash_salt，用于链接验证
    参数: partition(分区), music_hash(音乐hash), format_type(格式)
    返回: 加盐后的hash值
    """
    combined_string = f"{partition}_{music_hash}_{format_type}_{DOWNLOAD_KEY}"
    return hashlib.md5(combined_string.encode('utf-8')).hexdigest()

def rclone_move_to_remote(local_file_path, remote_path, filename):
    """
    使用rclone move将文件上传到远端
    参数:
    - local_file_path: 本地文件路径
    - remote_path: 远端路径 (如: s3:bucket-name)
    - filename: 文件名
    返回: (success: bool, error_message: str)
    """
    try:
        remote_file_path = f"{remote_path}/{filename}"
        cmd = [RCLONE_COMMAND, "--config", RCLONE_CONFIG_FILE, "move", local_file_path, remote_file_path]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            print(f"Successfully moved {local_file_path} to {remote_file_path}")
            return True, None
        else:
            error_msg = f"rclone move failed: {result.stderr}"
            print(error_msg)
            return False, error_msg

    except subprocess.TimeoutExpired:
        error_msg = "rclone move timeout"
        print(error_msg)
        return False, error_msg
    except Exception as e:
        error_msg = f"rclone move error: {str(e)}"
        print(error_msg)
        return False, error_msg

def rclone_copy_from_remote(remote_path, filename, local_dir):
    """
    使用rclone copy从远端下载文件到本地
    参数:
    - remote_path: 远端路径 (如: s3:bucket-name)
    - filename: 文件名
    - local_dir: 本地目录
    返回: (success: bool, local_file_path: str, error_message: str)
    """
    try:
        remote_file_path = f"{remote_path}/{filename}"
        local_file_path = os.path.join(local_dir, filename)

        # 确保本地目录存在
        os.makedirs(local_dir, exist_ok=True)

        cmd = [RCLONE_COMMAND, "--config", RCLONE_CONFIG_FILE, "copy", remote_file_path, local_dir]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

        if result.returncode == 0 and os.path.exists(local_file_path):
            print(f"Successfully copied {remote_file_path} to {local_file_path}")
            return True, local_file_path, None
        else:
            error_msg = f"rclone copy failed: {result.stderr}"
            print(error_msg)
            return False, None, error_msg

    except subprocess.TimeoutExpired:
        error_msg = "rclone copy timeout"
        print(error_msg)
        return False, None, error_msg
    except Exception as e:
        error_msg = f"rclone copy error: {str(e)}"
        print(error_msg)
        return False, None, error_msg

def cleanup_temp_file(file_path, max_age_seconds=3600):
    """
    清理临时文件
    参数:
    - file_path: 文件路径
    - max_age_seconds: 最大保留时间（秒），默认1小时
    """
    try:
        if os.path.exists(file_path):
            file_age = time.time() - os.path.getmtime(file_path)
            if file_age > max_age_seconds:
                os.remove(file_path)
                print(f"Cleaned up temp file: {file_path}")
    except Exception as e:
        print(f"Failed to cleanup temp file {file_path}: {str(e)}")

def download_audio(music_hash, title, artist, album, video_id, partition=0, temp_dir=TEMP_DIR, cookies_file=COOKIES_FILE):
    """
    优化的下载音频文件流程：
    1. 下载到临时上传文件夹
    2. 复制到临时下载文件夹（触发更新回调）
    3. 移动到rclone远端存储
    参数:
    - music_hash: 音乐的MD5哈希值
    - title, artist, album: 音乐元数据
    - video_id: YouTube视频ID
    - partition: 存储分区号
    """
    # 获取临时目录
    upload_dir = get_temp_upload_dir()
    download_dir = get_temp_download_dir()

    # 确保目录存在
    os.makedirs(upload_dir, exist_ok=True)
    os.makedirs(download_dir, exist_ok=True)
    os.makedirs(temp_dir, exist_ok=True)

    # 生成YouTube URL
    song_url = f'https://music.youtube.com/watch?v={video_id}'

    # 配置下载选项
    ydl_opts = {
        "extractor_args": {"youtube": {
            "skip": ["translated_subs"],
            'player_client': {'web_music'},
            "po_token":{
                "web_music.gvs+Mlu5L2WA-91KHbkXrS_YjXit2JMfxrm8LIi9kQ4XA1yazXkRNHwIB17hH9nZtRDo-M-mJ-82yPVjjk_Pgw3wJd1m94HfqH4d6K5r28pctk3Ay3sNV3VEXUVnHFDk"
            }
        }},
        'cookiefile': cookies_file,
        'format': '774/251',
        'postprocessors': [{
            'key': 'FFmpegMetadata',
            'add_metadata': True,
        }],
        'paths': {
            'temp': temp_dir,
        },
        'overwrites': True,
        'writethumbnail': True,
        'outtmpl': {
            'default': os.path.join(upload_dir, music_hash + '.%(ext)s')
        }
    }

    # 创建下载器实例并执行下载
    with YoutubeDL(ydl_opts) as ydl:
        try:
            # 先获取可用格式信息
            info = ydl.extract_info(song_url, download=True)
            print(f"选中的音频格式: {info['format']}")

            # 步骤1: 复制文件到临时下载文件夹（用于即时访问）
            copy_files_to_download_dir(music_hash, upload_dir, download_dir)
            print(f"Files copied to download directory for immediate access: {music_hash}")

            # 步骤2: 文件已经可以通过临时下载文件夹访问，可以立即触发更新回调
            # 后续的远端上传将在后台异步进行
            print(f"Download completed and files ready for immediate access: {music_hash}")

        except Exception as e:
            print(f"下载或上传出错: {str(e)}")
            # 清理可能残留的本地文件
            cleanup_failed_download(music_hash, upload_dir, download_dir)
            raise


def copy_files_to_download_dir(music_hash, upload_dir, download_dir):
    """
    将下载的文件复制到临时下载文件夹，用于即时访问
    """
    import shutil

    files_to_copy = [
        (f"{music_hash}.webm", "audio/webm"),
        (f"{music_hash}.webp", "image/webp"),
        (f"{music_hash}.jpg", "image/jpeg")
    ]

    for filename, _ in files_to_copy:
        src_path = os.path.join(upload_dir, filename)
        dst_path = os.path.join(download_dir, filename)

        if os.path.exists(src_path):
            try:
                shutil.copy2(src_path, dst_path)
                print(f"Copied {filename} to download directory")
            except Exception as e:
                print(f"Failed to copy {filename} to download directory: {str(e)}")


def upload_files_to_remote(music_hash, upload_dir, remote_path):
    """
    将文件上传到远端存储
    """
    files_to_upload = [
        (f"{music_hash}.webm", True),   # (filename, required)
        (f"{music_hash}.webp", False),
        (f"{music_hash}.jpg", False)
    ]

    for filename, required in files_to_upload:
        file_path = os.path.join(upload_dir, filename)

        if os.path.exists(file_path):
            success, error = rclone_move_to_remote(file_path, remote_path, filename)
            if not success:
                error_msg = f"Failed to upload {filename}: {error}"
                print(error_msg)
                if required:  # 音频文件上传失败则抛出异常
                    raise Exception(f"Required file upload failed: {error}")
                # 图片文件上传失败不阻止整个流程


def cleanup_failed_download(music_hash, upload_dir, download_dir):
    """
    清理失败下载的文件
    """
    for directory in [upload_dir, download_dir]:
        for ext in ['webm', 'webp', 'jpg']:
            temp_file = os.path.join(directory, f"{music_hash}.{ext}")
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    print(f"Cleaned up failed download file: {temp_file}")
                except Exception as e:
                    print(f"Failed to cleanup {temp_file}: {str(e)}")
