# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "La música no existe"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "Error al solicitar la descarga de la canción"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "La canción se está descargando"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "Tarea de descarga creada"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "Solicitud de descarga de la canción enviada, disponible más tarde"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "Método de solicitud no válido"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "Clave no válida"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "Falta el valor hash de la canción"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "Estado de descarga actualizado"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "La canción está lista para descargar, no es necesario actualizar nuevamente"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "Canción no encontrada"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "Registro de descarga no encontrado"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "Registro de descarga eliminado con éxito"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "Tarea de carga eliminada con éxito"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "El correo electrónico, la contraseña y el código de verificación no pueden estar vacíos"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "Fallo en la verificación del código, inténtelo de nuevo"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "Error en el encabezado del correo, envío fallido"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "Correo de verificación enviado, por favor revise su bandeja de entrada."

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "Enlace no válido o caducado"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "Datos de verificación incompletos"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "Correo electrónico o contraseña incorrectos"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "Configuración de usuario no encontrada"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "Se permiten un máximo de 5 configuraciones"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "Error de conexión WebDAV: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "Configuración no encontrada"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "Faltan campos obligatorios"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "Error en la solicitud de descarga"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "Error en la solicitud de carga"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "Tarea de carga creada"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "Configuración WebDAV no válida"

#: backend/webdav.py:145
msgid "未授权"
msgstr "No autorizado"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "Tarea no encontrada"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "Chino simplificado"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr "Árabe"

#: onemusic_backend/settings.py:133
msgid "German"
msgstr "Alemán"

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "Inglés"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr "Español"

#: onemusic_backend/settings.py:136
msgid "French"
msgstr "Francés"

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr "Hindi"

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr "Italiano"

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr "Japonés"

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr "Coreano"

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr "Holandés"

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr "Portugués"

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr "Ruso"

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr "Turco"

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr "Chino tradicional"
