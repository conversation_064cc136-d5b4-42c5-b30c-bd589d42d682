/**
 * Optimized Download Manager with parallel processing
 * Handles concurrent ffmpeg loading, download URL fetching, and file downloads
 */

import AudioTranscoder from './audioTranscoder';
import { fetchDownloadStatus, checkDownloadLink } from '../utils';

export class DownloadManager {
    constructor() {
        this.transcoder = null;
        this.abortController = null;
        this.progressComponentCallback = null;
        this.t = null; // Translation function
    }

    /**
     * Safe translation function that provides fallback
     */
    _translate(key) {
        if (this.t && typeof this.t === 'function') {
            return this.t(key);
        }
        // Fallback to English text if translation function is not available
        const fallbacks = {
            'download_cancelled': 'Download cancelled',
            'download_timeout': 'Download link retrieval timed out, please try again later.',
            'failed_to_download_audio': 'Failed to download audio file'
        };
        return fallbacks[key] || key;
    }

    /**
     * Update specific progress component
     * @param {string} component - Progress component name
     * @param {number} progress - Progress value for this component
     */
    _updateProgressComponent(component, progress) {
        if (this.progressComponentCallback) {
            this.progressComponentCallback(component, progress);
        }
    }

    /**
     * Process download with component-based progress tracking
     * @param {Object} songData - Song information
     * @param {string} requestFormat - Requested audio format
     * @param {Function} onProgressComponent - Component progress callback
     * @param {Function} t - Translation function
     */
    async processDownloadWithComponents(songData, requestFormat, onProgressComponent, t) {
        this.abortController = new AbortController();
        this.progressComponentCallback = onProgressComponent;
        this.t = t; // Store translation function for use in other methods

        try {
            // Reset progress state
            this.progressState = {
                urlFetch: 0,
                ffmpegLoad: 0,
                audioDownload: 0,
                transcoding: 0
            };

            this._updateProgressComponent('urlFetch', 0);

            // Initialize transcoder
            this.transcoder = new AudioTranscoder();

            // Set up transcoding progress callback (maps to 0-10% of transcoding component)
            this.transcoder.setProgressCallback(({ progress }) => {
                const transcodingProgress = progress * 10; // 0-10%
                this._updateProgressComponent('transcoding', transcodingProgress);
            });

            // Start parallel operations
            const ffmpegLoadPromise = requestFormat !== 'webm' ?
                this._loadFFmpegWithProgress() :
                this._skipFFmpegLoad();

            const downloadUrlPromise = this._getDownloadUrlWithProgress(songData);

            // Pre-fetch thumbnail (optional, don't block on failure)
            const thumbnailPromise = songData.thumbnail ?
                this._fetchThumbnail(songData.thumbnail) :
                Promise.resolve(null);

            // Get download URL first (don't wait for FFmpeg if not needed yet)
            const originalAudioUrl = await downloadUrlPromise;

            // Start audio download immediately after getting URL
            const audioDownloadPromise = this._fetchAudioWithProgress(originalAudioUrl);

            // For webm format, we don't need FFmpeg, so download and return immediately
            if (requestFormat === 'webm') {
                const audioBlob = await audioDownloadPromise;
                this._updateProgressComponent('transcoding', 10);
                this._downloadFile(audioBlob, `${songData.title} - ${songData.artist}.webm`);
                return;
            }

            // Wait for FFmpeg loading to complete
            await ffmpegLoadPromise;

            // Wait for audio download to complete
            const [audioBlob, imageBlob] = await Promise.all([
                audioDownloadPromise,
                thumbnailPromise
            ]);

            // Start transcoding
            this._updateProgressComponent('transcoding', 0);

            // Transcode audio (progress handled by transcoder callback)
            const transcodedData = await this.transcoder.transcodeAudio(
                audioBlob,
                imageBlob,
                requestFormat,
                {
                    title: songData.title,
                    artist: songData.artist,
                    album: songData.album
                }
            );

            // Transcoding complete
            this._updateProgressComponent('transcoding', 10);

            // Create and download transcoded file
            const blob = new Blob([transcodedData], {
                type: requestFormat === 'mp3' ? 'audio/mpeg' : 'audio/flac'
            });

            this._downloadFile(blob, `${songData.artist} - ${songData.title}.${requestFormat}`);
            this._updateProgressComponent('transcoding', 10);

        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error(this._translate("download_cancelled"));
            }
            throw error;
        }
    }



    /**
     * Load FFmpeg with progress updates
     */
    async _loadFFmpegWithProgress() {
        this._updateProgressComponent('ffmpegLoad', 0);
        await this.transcoder.load();
        this._updateProgressComponent('ffmpegLoad', 20);
        return true;
    }

    /**
     * Skip FFmpeg loading for webm format
     */
    async _skipFFmpegLoad() {
        this._updateProgressComponent('ffmpegLoad', 20);
        return true;
    }

    /**
     * Get download URL with retries and progress updates
     */
    async _getDownloadUrlWithProgress(songData) {
        const maxRetries = 20;
        const retryDelay = 2000;

        for (let retries = 0; retries < maxRetries; retries++) {
            if (this.abortController.signal.aborted) {
                throw new Error('Download cancelled');
            }

            // Update progress based on retry attempts (0-20% range)
            const progressStep = Math.min(20, (retries + 1) * (20 / maxRetries));
            this._updateProgressComponent('urlFetch', progressStep);

            try {
                const status = await fetchDownloadStatus(songData, 'download');
                if (status.download_url) {
                    const isReady = await checkDownloadLink(status.download_url);
                    if (isReady) {
                        this._updateProgressComponent('urlFetch', 20);
                        return status.download_url;
                    }
                }
            } catch (error) {
                console.warn(`Download URL fetch attempt ${retries + 1} failed:`, error);
            }

            // Wait before retry
            await this._delay(retryDelay);
        }

        throw new Error(this._translate("download_timeout"));
    }

    /**
     * Fetch audio file with progress tracking using fetch API and ReadableStream
     * Includes automatic retry logic for failed downloads
     */
    async _fetchAudioWithProgress(url) {
        const maxRetries = 3;
        const retryDelay = 1000; // 1-second base delay

        for (let attempt = 0; attempt < maxRetries; attempt++) {
            try {
                // Reset progress for each attempt
                this._updateProgressComponent('audioDownload', 0);

                // Add exponential backoff delay for retries
                if (attempt > 0) {
                    const delay = retryDelay * Math.pow(2, attempt - 1); // 1s, 2s, 4s...
                    console.log(`Audio download attempt ${attempt + 1}/${maxRetries}, waiting ${delay}ms before retry...`);
                    await this._delay(delay);
                }

                return await this._performAudioDownload(url);

            } catch (error) {
                // Don't retry if download was cancelled by user
                if (error.name === 'AbortError') {
                    throw new Error(this._translate("download_cancelled"));
                }

                console.warn(`Audio download attempt ${attempt + 1}/${maxRetries} failed:`, error.message);

                // If this was the last attempt, throw the error
                if (attempt === maxRetries - 1) {
                    throw new Error(this._translate("failed_to_download_audio") + ` (${maxRetries} attempts failed, last error: ${error.message})`);
                }
            }
        }
    }

    /**
     * Perform the actual audio download with progress tracking
     */
    async _performAudioDownload(url) {
        const response = await fetch(url, {
            signal: this.abortController.signal
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const contentLength = response.headers.get('content-length');
        const total = contentLength ? parseInt(contentLength, 10) : null;

        if (!total) {
            // If we can't get content length, fall back to time-based progress estimation
            return this._fetchAudioWithTimeBasedProgress(response);
        }

        const reader = response.body.getReader();
        const chunks = [];
        let loaded = 0;

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) {
                    break;
                }

                chunks.push(value);
                loaded += value.length;

                // Map download progress to 0-50% range for audioDownload component
                const downloadPercent = (loaded / total) * 100;
                const mappedProgress = (downloadPercent / 100) * 50; // 0-50%
                this._updateProgressComponent('audioDownload', mappedProgress);
            }
        } catch (error) {
            // Make sure to release the reader if an error occurs
            reader.releaseLock();
            throw error;
        }

        // Download completed
        this._updateProgressComponent('audioDownload', 50);

        // Combine all chunks into a single Uint8Array
        const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;

        for (const chunk of chunks) {
            result.set(chunk, offset);
            offset += chunk.length;
        }

        // Convert to Blob
        return new Blob([result]);
    }

    /**
     * Fallback method for when content-length is not available
     */
    async _fetchAudioWithTimeBasedProgress(response) {
        const reader = response.body.getReader();
        const chunks = [];
        const startTime = Date.now();

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) {
                    break;
                }

                chunks.push(value);

                // Time-based progress estimation (similar to original logic)
                const elapsed = (Date.now() - startTime) / 1000;
                const estimatedProgress = Math.min(45, elapsed * 2); // Slow increment up to 45%
                this._updateProgressComponent('audioDownload', estimatedProgress);
            }
        } catch (error) {
            // Make sure to release the reader if an error occurs
            reader.releaseLock();
            throw error;
        }

        // Download completed
        this._updateProgressComponent('audioDownload', 50);

        // Combine all chunks into a single Uint8Array
        const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;

        for (const chunk of chunks) {
            result.set(chunk, offset);
            offset += chunk.length;
        }

        // Convert to Blob
        return new Blob([result]);
    }

    /**
     * Fetch audio file with error handling (fallback method)
     */
    async _fetchAudio(url) {
        const response = await fetch(url, {
            signal: this.abortController.signal
        });

        if (!response.ok) {
            throw new Error(this._translate("failed_to_download_audio"));
        }

        return response.blob();
    }

    /**
     * Fetch thumbnail with graceful failure
     */
    async _fetchThumbnail(thumbnailUrl) {
        try {
            const response = await fetch(thumbnailUrl, {
                signal: this.abortController.signal
            });
            
            return response.ok ? response.blob() : null;
        } catch (error) {
            console.warn('Thumbnail fetch failed:', error);
            return null;
        }
    }

    /**
     * Download file to user's device
     */
    _downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        // Clean up object URL
        setTimeout(() => URL.revokeObjectURL(url), 100);
    }

    /**
     * Utility delay function
     */
    _delay(ms) {
        return new Promise(resolve => {
            const timeoutId = setTimeout(resolve, ms);
            
            // Allow cancellation
            this.abortController.signal.addEventListener('abort', () => {
                clearTimeout(timeoutId);
                resolve();
            });
        });
    }

    /**
     * Cancel ongoing download
     */
    cancel() {
        if (this.abortController) {
            this.abortController.abort();
        }
    }

    /**
     * Clean up resources
     */
    cleanup() {
        this.cancel();
        
        if (this.transcoder) {
            this.transcoder.terminate();
            this.transcoder = null;
        }
        
        this.abortController = null;
    }
}

export default DownloadManager;
