{"Home": {"title": "1Music.cc – Free High-Quality Music Downloads, Supports FLAC/MP3, WebDAV Uploads", "description": "1Music.cc offers a rich collection of high-quality music downloads, supporting FLAC and MP3 formats. One-click upload to WebDAV. Enjoy a vast library of lossless music with fast downloads and an ultimate sound experience!", "login": "<PERSON><PERSON>", "darkMode": "Dark Mode", "setLanguage": "Set Language", "manageUser": "Manage User", "searchPlaceholder": "Search...", "verificationPending": "Completing verification, please wait..."}, "Login": {"email": "Email", "password": "Password", "login": "<PERSON><PERSON>", "loginSuccess": "Login Successful", "forgotPassword": "Forgot Password", "noAccount": "No account? Sign up now", "notice": "Notice", "unknownError": "An unknown error occurred", "close": "Close"}, "Register": {"email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "invalidEmail": "Please enter a valid email address", "loginSuccess": "Login Successful", "passwordLength": "Password length must be between 10 and 50 characters", "passwordMismatch": "Passwords do not match", "sending": "Sending...", "sendVerification": "Send Verification Link", "loggingIn": "Logging in...", "verifiedLogin": "I have verified, login directly", "verificationSent": "Verification link sent, please check your email", "verificationFailed": "Failed to send verification link", "unknownError": "An unknown error occurred", "confirm": "Confirm", "error": "Error", "tip": "Tip", "sendAgain": "Didn't receive it? <PERSON>sen<PERSON>"}, "User": {"profile": "Profile", "downloaded": "Downloaded", "uploadTasks": "Upload Tasks"}, "ProfilePage": {"user_info": "User Information", "premium": "(Premium)", "webdav_config": "WebDAV Configuration {index}", "webdav_url": "WebDAV URL", "webdav_url_placeholder": "Please enter WebDAV URL", "username": "Username", "username_placeholder": "Please enter username", "password": "Password", "password_placeholder": "Please enter password", "test_and_save": "Test Connection & Save", "connection_success": "Saved", "delete_config": "Delete Configuration", "add_another_webdav": "Add Another WebDAV Configuration", "add_webdav": "Add WebDAV Configuration", "config_saved": "Configuration Saved", "test_failed": "WebDAV Connection Test Failed", "incomplete_config": "Please complete the WebDAV configuration", "remove_failed": "Failed to delete WebDAV configuration"}, "Download": {"download": "Download", "downloading": "Downloading {title}", "download_timeout": "Download link retrieval timed out, please try again later.", "download_unavailable": "Download link is temporarily unavailable, please try again later.", "download_song": "Download {title}", "upload_to_webdav": "Upload to WebDAV", "select_webdav_config": "Select WebDAV Configuration:", "upload_error": "Upload Failed", "webdav_config_not_found": "Selected WebDAV configuration not found", "incomplete_song_info": "Song information is incomplete", "upload_success": "{title} has been added to the upload queue. Check the user page later.", "cancel": "Cancel", "preparing_download": "Preparing download...", "fetching_audio": "Fetching audio file...", "downloading_files": "Downloading files...", "transcoding_audio": "Converting audio format...", "download_complete": "Download complete!", "download_url_ready": "Download URL ready", "loading_transcoder": "Loading transcoder...", "transcoder_ready": "Transcoder ready", "download_complete_preparing": "File download complete, preparing...", "download_cancelled": "Download cancelled", "download_failed": "Download failed", "failed_to_download_audio": "Failed to download audio file", "try_again_later": "Please try again later", "check_downloads_folder": "Check your downloads folder", "processing": "Processing...", "loading": "Loading...", "fetch_error": "An error occurred", "select_format": "Select Format", "mp3": "MP3", "flac": "FLAC", "select_webdav": "Please select a WebDAV configuration"}, "RechargeDialog": {"supportAuthor": "Support the Author", "anyAmount": "Support with any amount and remove ads", "cancel": "Cancel", "confirmPayment": "Confirm Payment", "processing": "Processing...", "fetchError": "Failed to retrieve product list", "paymentInitError": "Payment initialization failed", "requestFailed": "Request failed, please try again"}}