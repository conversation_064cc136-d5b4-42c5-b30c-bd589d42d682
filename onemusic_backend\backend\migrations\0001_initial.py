# Generated by Django 5.1.5 on 2025-02-15 05:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Music',
            fields=[
                ('hash', models.CharField(db_index=True, max_length=255, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('artist', models.CharField(max_length=255)),
                ('album', models.Char<PERSON>ield(max_length=255)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('could_download', models.BooleanField(default=False)),
                ('download_count', models.IntegerField(default=0)),
            ],
        ),
        migrations.CreateModel(
            name='RedeemCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=32, unique=True)),
                ('points', models.IntegerField()),
                ('expiration_date', models.DateTimeField()),
            ],
        ),
        migrations.CreateModel(
            name='Profile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('points', models.IntegerField(default=5)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='RedemptionRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('redeemed_at', models.DateTimeField(auto_now_add=True)),
                ('redeem_code', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='backend.redeemcode')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UploadTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('processing', 'Processing'), ('success', 'Success'), ('failed', 'Failed')], default='processing', max_length=20)),
                ('reason', models.TextField(blank=True, null=True)),
                ('music', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='upload_tasks', to='backend.music')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='upload_tasks', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='WebdavConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(max_length=255)),
                ('username', models.CharField(max_length=150)),
                ('password', models.CharField(max_length=128)),
                ('is_tested', models.BooleanField(default=False)),
                ('profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='webdav_config', to='backend.profile')),
            ],
        ),
        migrations.CreateModel(
            name='UserDownloadedMusic',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('downloaded_at', models.DateTimeField(auto_now_add=True)),
                ('music', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='backend.music')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='downloaded_music', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['user', 'downloaded_at'], name='backend_use_user_id_6e7f04_idx')],
            },
        ),
    ]
