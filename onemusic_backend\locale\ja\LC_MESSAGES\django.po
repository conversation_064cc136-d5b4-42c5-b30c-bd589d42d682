# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "音楽は存在しません"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "楽曲のダウンロードリクエストに失敗しました"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "楽曲をダウンロード中"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "ダウンロードタスクが作成されました"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "楽曲のダウンロードリクエストが送信されました。後で利用可能です"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "無効なリクエスト方法"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "無効なキー"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "楽曲のハッシュ値がありません"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "ダウンロードのステータスが更新されました"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "楽曲のダウンロード準備が完了しました。再度更新する必要はありません"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "楽曲が見つかりませんでした"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "ダウンロード履歴が見つかりませんでした"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "ダウンロード履歴が正常に削除されました"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "アップロードタスクが正常に削除されました"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "メールアドレス、パスワード、確認コードは空にできません"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "確認コードの検証に失敗しました。もう一度お試しください"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "メールヘッダーエラーにより送信に失敗しました"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "確認メールが送信されました。受信ボックスを確認してください"

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "リンクが無効または期限切れです"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "確認データが不完全です"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "メールアドレスまたはパスワードが間違っています"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "ユーザー設定が見つかりませんでした"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "最大5つの設定が許可されています"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "WebDAV接続に失敗しました: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "設定が見つかりませんでした"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "必要なフィールドが不足しています"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "ダウンロードリクエストに失敗しました"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "アップロードリクエストに失敗しました"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "アップロードタスクが作成されました"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "無効な WebDAV 設定"

#: backend/webdav.py:145
msgid "未授权"
msgstr "未承認"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "タスクが見つかりませんでした"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "簡体字中国語"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr "アラビア語"

#: onemusic_backend/settings.py:133
msgid "German"
msgstr "ドイツ語"

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "英語"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr "スペイン語"

#: onemusic_backend/settings.py:136
msgid "French"
msgstr "フランス語"

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr "ヒンディー語"

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr "イタリア語"

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr "日本語"

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr "韓国語"

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr "オランダ語"

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr "ポルトガル語"

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr "ロシア語"

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr "トルコ語"

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr "繁体字中国語"
