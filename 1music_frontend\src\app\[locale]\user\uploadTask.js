import { useEffect, useState } from 'react';
import { Box, TableContainer, Table, TableHead, TableRow, TableCell, TableBody, IconButton, Stack, Pagination, Typography } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import axios from 'axios';
import {backendUrl} from "@/src/app/[locale]/config";

const UserUploadTasks = () => {
    const [uploadTasks, setUploadTasks] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);

    useEffect(() => {
        fetchUploadTasks(currentPage);
    }, [currentPage]);

    const fetchUploadTasks = async (page) => {
        try {
            const response = await axios.get(`${backendUrl}user_upload_tasks/?page=${page}`,{
                withCredentials:true
            });
            setUploadTasks(response.data.upload_tasks);
            setTotalPages(response.data.total_pages);
        } catch (error) {
            console.error('Error fetching upload tasks:', error);
        }
    };

    const handleDelete = async (task) => {
        try {
            await axios.delete(`${backendUrl}delete_task/${task.task_id}/`,{
                withCredentials:true
            });
            fetchUploadTasks(currentPage); // 重新获取数据
        } catch (error) {
            console.error('Error deleting task:', error);
        }
    };

    const handlePageChange = (event, page) => {
        setCurrentPage(page);
    };

    return (
        <Box p={0}>
            <TableContainer>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell sx={{ width: '40%', wordWrap: 'break-word' }}>Title</TableCell>
                            <TableCell sx={{ width: '30%', wordWrap: 'break-word' }}>Artist</TableCell>
                            <TableCell sx={{ width: '20%' }}>Status</TableCell>
                            <TableCell sx={{ width: '10%' }} align="center">Actions</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {uploadTasks.map((task, index) => (
                            <TableRow key={index}>
                                <TableCell sx={{ wordWrap: 'break-word' }}>{task.music_title}</TableCell>
                                <TableCell sx={{ wordWrap: 'break-word' }}>{task.music_artist}</TableCell>
                                <TableCell>{task.status}</TableCell>
                                <TableCell align="center">
                                    <Stack direction="row" spacing={1} justifyContent="center">
                                        <IconButton color="error" onClick={() => handleDelete(task)}>
                                            <DeleteIcon />
                                        </IconButton>
                                    </Stack>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
            {uploadTasks.length > 0 ? (
                <Pagination
                    count={totalPages}
                    page={currentPage}
                    onChange={handlePageChange}
                    color="primary"
                    sx={{ mt: 3 }}
                />
            ) : (
                <Typography variant="h6" color="textSecondary" sx={{ mt: 3, textAlign: 'center' }}>
                    No uploaded records
                </Typography>
            )}
        </Box>
    );
};

export default UserUploadTasks;