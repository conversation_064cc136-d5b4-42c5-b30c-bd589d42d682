# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "Musik existiert nicht"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "Anfrage zum Herunterladen des Songs fehlgeschlagen"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "Das Lied wird heruntergeladen"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "Download-Aufgabe wurde erstellt"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "Anfrage zum Herunterladen des Songs wurde eingereicht, später verfügbar"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "Ungültige Anfragemethode"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "Ungültiger Schlüssel"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "Fehlender Song-Hash-Wert"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "Download-Status wurde aktualisiert"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "Das Lied ist bereit zum Herunterladen, kein erneutes Update erforderlich"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "Lied nicht gefunden"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "Download-Datensatz nicht gefunden"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "Download-Datensatz erfolgreich gelöscht"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "Upload-Aufgabe erfolgreich gelöscht"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "E-Mail, Passwort und Bestätigungscode dürfen nicht leer sein"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "Bestätigungscode ungültig, bitte erneut versuchen"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "Fehler im E-Mail-Header, Senden fehlgeschlagen"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "Bestätigungs-E-Mail wurde gesendet, bitte überprüfen Sie Ihr Postfach."

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "Ungültiger oder abgelaufener Link"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "Unvollständige Verifizierungsdaten"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "E-Mail oder Passwort ist falsch"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "Benutzerkonfiguration nicht gefunden"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "Maximal 5 Konfigurationen erlaubt"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "WebDAV-Verbindung fehlgeschlagen: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "Konfiguration nicht gefunden"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "Erforderliche Felder fehlen"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "Download-Anfrage fehlgeschlagen"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "Upload-Anfrage fehlgeschlagen"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "Upload-Aufgabe wurde erstellt"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "Ungültige WebDAV-Konfiguration"

#: backend/webdav.py:145
msgid "未授权"
msgstr "Nicht autorisiert"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "Aufgabe nicht gefunden"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "Vereinfachtes Chinesisch"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr "Arabisch"

#: onemusic_backend/settings.py:133
msgid "German"
msgstr "Deutsch"

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "Englisch"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr "Spanisch"

#: onemusic_backend/settings.py:136
msgid "French"
msgstr "Französisch"

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr "Hindi"

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr "Italienisch"

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr "Japanisch"

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr "Koreanisch"

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr "Niederländisch"

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr "Portugiesisch"

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr "Russisch"

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr "Türkisch"

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr "Traditionelles Chinesisch"

