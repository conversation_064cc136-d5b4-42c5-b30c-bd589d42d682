import datetime
import os

DOWNLOAD_KEY = '1p5kh&FPTA9W2VLF=n6~&J]u1cx'
SECRET_KEY = 'django-insecure-enh3gcir$=4qr$wt)lk4rnw57_y8%hk&1apc9#)m6=p+_p5j)s'

# 基础目录配置
TEMP_UPLOAD_DIR = "./temp_upload"      # 临时上传文件夹
TEMP_DOWNLOAD_DIR = "./temp_download"  # 临时下载文件夹
TEMP_DIR = "./Temp"                    # yt-dlp临时目录
COOKIES_FILE = "cookies.txt"
BACKEND_URL = "https://1music.cc/backend/"

# rclone配置
RCLONE_COMMAND = "rclone"  # rclone命令路径，如果不在PATH中需要指定完整路径
RCLONE_CONFIG_FILE = "./rclone.conf"  # rclone配置文件路径

# 分区配置
DEFAULT_PARTITION = 0
MAX_PARTITIONS = 10

# 临时文件清理配置
TEMP_FILE_MAX_AGE_SECONDS = 3600  # 1小时后清理临时文件
DOWNLOAD_DIR_MAX_AGE_SECONDS = 7200  # 临时下载文件夹保留2小时（用于缓存）

# 分区到远端路径的映射
PARTITION_REMOTE_MAPPING = {
    0: "1music_:/Music",      #分区0
    1: "s3:1music-partition-1",      # 分区1 -> S3存储桶
    2: "gdrive:1music/partition-2",  # 分区2 -> Google Drive
    3: "onedrive:1music/partition-3", # 分区3 -> OneDrive
    # 可以根据需要添加更多分区映射
}

def get_remote_path(partition=DEFAULT_PARTITION):
    """获取指定分区的远端路径"""
    return PARTITION_REMOTE_MAPPING.get(partition, PARTITION_REMOTE_MAPPING[DEFAULT_PARTITION])

def get_temp_upload_dir():
    """获取临时上传目录"""
    os.makedirs(TEMP_UPLOAD_DIR, exist_ok=True)
    return TEMP_UPLOAD_DIR

def get_temp_download_dir():
    """获取临时下载目录"""
    os.makedirs(TEMP_DOWNLOAD_DIR, exist_ok=True)
    return TEMP_DOWNLOAD_DIR

# 兼容性配置（保持向后兼容）
OUTPUT_DIR = get_temp_upload_dir()
DOWNLOAD_DIR = get_temp_upload_dir()

# JWT配置（已废弃，保留用于兼容性）
JWT_CONFIG = {
    'ALGORITHM': 'HS256',
    'EXPIRATION_DELTA': datetime.timedelta(minutes=30),
}