# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "La musique n'existe pas"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "Échec de la demande de téléchargement de la chanson"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "La chanson est en cours de téléchargement"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "Tâche de téléchargement créée"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "La demande de téléchargement de la chanson a été soumise, disponible plus tard"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "Méthode de requête invalide"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "Clé invalide"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "Valeur de hachage de la chanson manquante"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "Statut du téléchargement mis à jour"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "La chanson est prête à être téléchargée, pas besoin de mise à jour supplémentaire"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "Chanson non trouvée"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "Historique de téléchargement non trouvé"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "Historique de téléchargement supprimé avec succès"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "Tâche de téléchargement supprimée avec succès"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "L'e-mail, le mot de passe et le code de vérification ne peuvent pas être vides"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "Échec de la vérification du code, veuillez réessayer"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "Erreur d'en-tête de l'e-mail, envoi échoué"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "E-mail de vérification envoyé, veuillez vérifier votre boîte de réception."

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "Lien invalide ou expiré"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "Données de vérification incomplètes"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "E-mail ou mot de passe incorrect"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "Configuration utilisateur non trouvée"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "Un maximum de 5 configurations est autorisé"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "Échec de la connexion WebDAV: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "Configuration non trouvée"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "Champs requis manquants"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "Échec de la demande de téléchargement"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "Échec de la demande de téléversement"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "Tâche de téléversement créée"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "Configuration WebDAV invalide"

#: backend/webdav.py:145
msgid "未授权"
msgstr "Non autorisé"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "Tâche non trouvée"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "Chinois simplifié"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr "Arabe"

#: onemusic_backend/settings.py:133
msgid "German"
msgstr "Allemand"

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "Anglais"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr "Espagnol"

#: onemusic_backend/settings.py:136
msgid "French"
msgstr "Français"

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr "Hindi"

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr "Italien"

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr "Japonais"

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr "Coréen"

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr "Néerlandais"

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr "Portugais"

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr "Russe"

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr "Turc"

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr "Chinois traditionnel"
