import uuid
from functools import wraps

from django.shortcuts import render, redirect, get_object_or_404
from django.utils import timezone
from datetime import timedelta
from .models import RedeemCode

def admin_required(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if request.user.is_authenticated and request.user.is_staff:
            return view_func(request, *args, **kwargs)
        return redirect('/backend/adminlogin/?next=' + request.path)

    return _wrapped_view

@admin_required
def manage_redeem_codes(request):
    if request.method == "POST":
        points = int(request.POST.get("points", 0))
        expiration_date = timezone.now() + timedelta(days=7)
        RedeemCode.objects.create(points=points, expiration_date=expiration_date,code=uuid.uuid4())
        return redirect("manage_redeem_codes")

    redeem_codes = RedeemCode.objects.all().order_by('-expiration_date')
    return render(request, "redeem_codes.html", {"redeem_codes": redeem_codes})

@admin_required
def delete_code(request, code_id):
    code = get_object_or_404(RedeemCode, id=code_id)
    code.delete()
    return redirect("manage_redeem_codes")