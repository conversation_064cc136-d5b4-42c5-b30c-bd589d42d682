'use client';

import { useEffect, useRef, useState } from 'react';
import { CircularProgress, Box, Typography, LinearProgress } from '@mui/material';
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl';
import NoSsr from "@mui/material/NoSsr";
import DownloadManager from '@/src/app/[locale]/utils/downloadManager';

const DownloadPage = () => {
    const searchParams = useSearchParams()
    const t = useTranslations("Download");
    const title = searchParams.get('title');
    const album = searchParams.get('album');
    const artist = searchParams.get('artist');
    const videoId = searchParams.get('videoId');
    const request_format = searchParams.get('request_format');
    const song_hash = searchParams.get('song_hash');
    const thumbnail = searchParams.get('thumbnail');

    // Simple state management for parallel progress
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [progress, setProgress] = useState(0);

    // Progress state for parallel operations
    const progressState = useRef({
        urlFetch: 0,        // 获取链接进度 (0-20%)
        ffmpegLoad: 0,      // FFmpeg加载进度 (0-20%)
        audioDownload: 0,   // 音频下载进度 (0-50%)
        transcoding: 0      // 转码进度 (0-10%)
    });

    const downloadManagerRef = useRef(null);

    // Calculate total progress from all components
    const calculateTotalProgress = () => {
        const { urlFetch, ffmpegLoad, audioDownload, transcoding } = progressState.current;
        return Math.min(100, urlFetch + ffmpegLoad + audioDownload + transcoding);
    };

    // Update specific progress component
    const updateProgressComponent = (component, componentProgress) => {
        progressState.current[component] = componentProgress;
        const totalProgress = calculateTotalProgress();
        setProgress(totalProgress);
    };

    useEffect(() => {
        const script = document.createElement("script");
        script.type = "text/javascript";
        script.src = "//instantdeceived.com/d6/2b/a5/d62ba5b1fd262e193f9593ba8ecde9d6.js";
        document.head.appendChild(script);

        const script_1 = document.createElement("script");
        script_1.type = "text/javascript";
        script_1.src = "//instantdeceived.com/81/53/df/8153df5d8be8fece95aa655e200165f1.js";
        document.body.appendChild(script_1);

        if (!title || !videoId || !request_format || !song_hash) {
            setError(t("incomplete_song_info"));
            setIsLoading(false);
            return;
        }

        const processDownload = async () => {
            try {
                // Reset progress state
                progressState.current = {
                    urlFetch: 0,
                    ffmpegLoad: 0,
                    audioDownload: 0,
                    transcoding: 0
                };
                setProgress(0);
                setError(null);

                // Initialize download manager
                downloadManagerRef.current = new DownloadManager();

                const songData = {
                    title,
                    album,
                    artist,
                    videoId,
                    request_format: 'webm', // Always request webm for transcoding
                    song_hash,
                    thumbnail
                };

                // Process download with component-based progress
                await downloadManagerRef.current.processDownloadWithComponents(
                    songData,
                    request_format,
                    updateProgressComponent,
                    t
                );

                setIsLoading(false);

            } catch (err) {
                console.error('Download error:', err);
                setError(err.message || t("download_failed"));
                setIsLoading(false);
            } finally {
                // Clean up download manager
                if (downloadManagerRef.current) {
                    downloadManagerRef.current.cleanup();
                    downloadManagerRef.current = null;
                }
            }
        };

        processDownload();

        // Cleanup function
        return () => {
            if (downloadManagerRef.current) {
                downloadManagerRef.current.cleanup();
                downloadManagerRef.current = null;
            }
        };
    }, [title, album, artist, videoId, request_format, song_hash, thumbnail, t]);

    return (
        <NoSsr>
            <Box display="flex" justifyContent="center" alignItems="center" flexDirection="column" height="90vh" sx={{ p: 3 }}>
                {isLoading ? (
                    <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" sx={{ minWidth: 300 }}>
                        <CircularProgress size={60} />
                        <Typography variant="h6" sx={{ mt: 2, mb: 3, textAlign: 'center' }}>
                            {title ? t("downloading", {title: title}) : t("processing")}
                        </Typography>
                        {progress > 0 && (
                            <Box sx={{ width: '100%', mt: 2 }}>
                                <LinearProgress variant="determinate" value={progress} />
                            </Box>
                        )}
                    </Box>
                ) : error ? (
                    <Box display="flex" flexDirection="column" alignItems="center">
                        <Typography variant="h6" color="error" sx={{ mb: 2, textAlign: 'center' }}>
                            {error}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                            {t("try_again_later")}
                        </Typography>
                    </Box>
                ) : (
                    <Box display="flex" flexDirection="column" alignItems="center">
                        <Typography variant="h6" color="success.main" sx={{ textAlign: 'center' }}>
                            {t("download_complete")}
                        </Typography>
                    </Box>
                )}
            </Box>
        </NoSsr>
    );
};

export default DownloadPage;

