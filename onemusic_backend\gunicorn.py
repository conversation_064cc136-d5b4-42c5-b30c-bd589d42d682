bind = "0.0.0.0:8000"

# 进程数（通常为 CPU 核心数 * 2 + 1）
workers = 9

# 使用的 worker 类型（常见的有 sync、eventlet、gevent、uvicorn.workers.UvicornWorker 等）
worker_class = "gevent"

# 设置超时时间，避免长时间无响应进程挂掉
timeout = 30
graceful_timeout = 25

# 是否开启后台守护进程模式（True 代表以守护进程方式运行）
daemon = False

# 访问日志和错误日志
accesslog = "-"  # 使用 "-" 代表输出到标准输出
errorlog = "gunicorn_error.log"

# 日志级别（debug, info, warning, error, critical）
loglevel = "info"

# 进程名称
tmp_name = "gunicorn_app"

# 最大请求数（避免内存泄漏，每个 worker 处理多少请求后重启）
max_requests = 1000
max_requests_jitter = 50  # 让 worker 以不同的时间间隔重启，避免同时重启导致负载波动

# 预加载应用（提升性能，适用于 preload_app=True 的场景）
preload_app = True

