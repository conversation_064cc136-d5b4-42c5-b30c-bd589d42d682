# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "音樂不存在"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "請求歌曲下載失敗"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "歌曲正在下載中"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "下載任務已建立"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "歌曲下載請求已提交，稍後可用"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "無效的請求方法"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "無效的密鑰"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "缺少歌曲哈希值"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "下載狀態已更新"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "歌曲已準備好下載，無需重複更新"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "歌曲未找到"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "下載記錄未找到"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "下載記錄刪除成功"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "上傳任務刪除成功"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "電子郵件、密碼和驗證碼不能為空"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "驗證碼驗證失敗，請重試"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "郵件標頭錯誤，發送失敗"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "驗證郵件已發送，請查收。"

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "鏈接無效或已過期"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "驗證數據不完整"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "電子郵件或密碼錯誤"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "用戶配置未找到"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "最多允許 5 個配置"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "WebDAV 連接失敗: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "配置未找到"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "缺少必要字段"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "請求下載失敗"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "請求上傳失敗"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "上傳任務已建立"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "無效的 WebDAV 配置"

#: backend/webdav.py:145
msgid "未授权"
msgstr "未授權"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "任務未找到"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "簡體中文"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr "阿拉伯語"

#: onemusic_backend/settings.py:133
msgid "German"
msgstr "德語"

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "英語"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr "西班牙語"

#: onemusic_backend/settings.py:136
msgid "French"
msgstr "法語"

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr "印地語"

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr "意大利語"

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr "日語"

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr "韓語"

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr "荷蘭語"

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr "葡萄牙語"

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr "俄語"

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr "土耳其語"

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr "繁體中文"