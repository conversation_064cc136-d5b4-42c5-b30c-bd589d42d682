'use client'
import React, { useState } from 'react';
import axios from 'axios';
import { <PERSON>Field, Button, Dialog, DialogTitle, DialogContent, DialogActions, Alert, createTheme, CssBaseline } from '@mui/material';
import { makeStyles, ThemeProvider } from '@mui/styles';
import NoSsr from '@mui/material/NoSsr';
import { backendUrl } from "@/src/app/[locale]/config";
import Typography from "@mui/material/Typography";
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import SEO from "@/src/app/[locale]/component/Seo";

const useStyles = makeStyles(() => ({
    linearGradientButton: {
        position: 'relative',
        overflow: 'hidden',
        fontWeight: 'bold',
        borderRadius: '4px',
        '& > span': {
            position: 'relative',
            zIndex: 1,
            color: 'white',
            fontSize: '16px',
            fontFamily: 'Roboto, Arial, sans-serif',
            fontWeight: 'bold',
        },
        '&::before': {
            content: '""',
            position: 'absolute',
            inset: '0',
            background: 'linear-gradient(135deg, #6253e1, #04befe)',
            zIndex: 0,
            borderRadius: 'inherit',
            transition: 'opacity 0.3s',
            opacity: 1,
        },
        '&:hover::before': {
            opacity: 0,
        },
    },
}));

// 创建暗色主题
const theme = createTheme({
    colorSchemes: {
        dark: true,
    },
});

const Home = () => {
    const classes = useStyles();
    const t = useTranslations('Login');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [dialogOpen, setDialogOpen] = useState(false);
    const [dialogMessage, setDialogMessage] = useState('');
    const [alertSeverity, setAlertSeverity] = useState('info');

    const handleLogin = async () => {
        try {
            const response = await axios.post(backendUrl + 'login/', {
                email,
                password,
            }, {
                withCredentials: true
            });

            if (response.data.detail === '登录成功') {
                window.location.href = '/';
            } else {
                setAlertSeverity('error');
            }
            setDialogMessage(t("loginSuccess"))
        } catch (error) {
            setAlertSeverity('error');
            setDialogMessage(error.response?.data?.detail || t('unknownError'));
        } finally {
            setDialogOpen(true);
        }
    };

    const handleDialogClose = () => {
        setDialogOpen(false);
    };

    return (
        <NoSsr>
            <ThemeProvider theme={theme}>
                <CssBaseline />
                <SEO />
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100vh', padding: '0 20px' }}>
                <Typography variant="h6" component="div" sx={{
                    fontFamily: "HarmonyOS Sans,system-ui",
                    letterSpacing: -0.5,
                    fontWeight: 'bold',
                    fontSize: '30px',
                    textAlign: 'center',
                    paddingBottom: '30px'
                }}>
                    1Music.cc
                </Typography>
                <div style={{ width: '100%', maxWidth: '400px', display: 'flex', flexDirection: 'column', gap: '20px' }}>
                    <TextField
                        label={t('email')}
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        fullWidth
                        required
                    />
                    <TextField
                        label={t('password')}
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        fullWidth
                        required
                    />
                    <Button
                        variant="contained"
                        onClick={handleLogin}
                        className={classes.linearGradientButton}
                    >
                        <span>{t('login')}</span>
                    </Button>
                    <Link href="/register" style={{ textAlign: 'center', textDecoration: 'none', color: '#007BFF' }}>
                        {t('forgotPassword')}
                    </Link>
                    <Link href="/register" style={{ textAlign: 'center', textDecoration: 'none', color: '#007BFF' }}>
                        {t('noAccount')}
                    </Link>
                </div>

                <Dialog open={dialogOpen} onClose={handleDialogClose}>
                    <DialogTitle>{t('notice')}</DialogTitle>
                    <DialogContent>
                        <Alert severity={alertSeverity}>{dialogMessage}</Alert>
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={handleDialogClose} color="primary">
                            {t('close')}
                        </Button>
                    </DialogActions>
                </Dialog>
            </div>
            </ThemeProvider>
        </NoSsr>
    );
};

export default Home;

