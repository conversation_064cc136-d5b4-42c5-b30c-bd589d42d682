# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-01 10:14+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: backend/downloader.py:28 backend/downloader.py:118 backend/webdav.py:65
msgid "音乐不存在"
msgstr "Music does not exist"

#: backend/downloader.py:52 backend/downloader.py:71 backend/downloader.py:100
#: backend/downloader.py:150
msgid "请求歌曲下载失败"
msgstr "Failed to request song download"

#: backend/downloader.py:53 backend/downloader.py:125
msgid "歌曲正在下载中"
msgstr "The song is being downloaded"

#: backend/downloader.py:77 backend/downloader.py:129
msgid "下载任务已创建"
msgstr "Download task created"

#: backend/downloader.py:102 backend/downloader.py:151
msgid "歌曲下载请求已提交，稍后可用"
msgstr "Song download request submitted, available later"

#: backend/downloader.py:104 backend/downloader.py:153
#: backend/downloader.py:181
msgid "无效的请求方法"
msgstr "Invalid request method"

#: backend/downloader.py:163
msgid "无效的密钥"
msgstr "Invalid key"

#: backend/downloader.py:167
msgid "缺少歌曲哈希值"
msgstr "Missing song hash value"

#: backend/downloader.py:174
msgid "下载状态已更新"
msgstr "Download status updated"

#: backend/downloader.py:176
msgid "歌曲已准备好下载，无需重复更新"
msgstr "The song is ready for download, no need to update again"

#: backend/downloader.py:179
msgid "歌曲未找到"
msgstr "Song not found"

#: backend/user.py:67
msgid "下载记录未找到"
msgstr "Download record not found"

#: backend/user.py:72
msgid "下载记录删除成功"
msgstr "Download record deleted successfully"

#: backend/user.py:128
msgid "上传任务删除成功"
msgstr "Upload task deleted successfully"

#: backend/views.py:32
msgid "邮箱、密码和验证码不能为空"
msgstr "Email, password, and verification code cannot be empty"

#: backend/views.py:52
msgid "验证码验证失败，请重试"
msgstr "Verification code validation failed, please try again"

#: backend/views.py:71
msgid "邮件头错误，发送失败"
msgstr "Email header error, sending failed"

#: backend/views.py:75
msgid "验证邮件已发送，请查收。"
msgstr "Verification email sent, please check your inbox."

#: backend/views.py:84
msgid "链接无效或已过期"
msgstr "Invalid or expired link"

#: backend/views.py:90
msgid "验证数据不完整"
msgstr "Incomplete verification data"

#: backend/views.py:118
msgid "邮箱或密码错误"
msgstr "Incorrect email or password"

#: backend/webdav.py:29
msgid "用户配置未找到"
msgstr "User configuration not found"

#: backend/webdav.py:32
msgid "最多允许5个配置"
msgstr "A maximum of 5 configurations are allowed"

#: backend/webdav.py:37
msgid "WebDAV 连接失败: "
msgstr "WebDAV connection failed: "

#: backend/webdav.py:52
msgid "配置未找到"
msgstr "Configuration not found"

#: backend/webdav.py:70
msgid "缺少必要字段"
msgstr "Missing required fields"

#: backend/webdav.py:88 backend/webdav.py:115
msgid "请求下载失败"
msgstr "Download request failed"

#: backend/webdav.py:135
msgid "请求上传失败"
msgstr "Upload request failed"

#: backend/webdav.py:136
msgid "上传任务已创建"
msgstr "Upload task created"

#: backend/webdav.py:138
msgid "无效的 WebDAV 配置"
msgstr "Invalid WebDAV configuration"

#: backend/webdav.py:145
msgid "未授权"
msgstr "Unauthorized"

#: backend/webdav.py:158
msgid "任务未找到"
msgstr "Task not found"

#: onemusic_backend/settings.py:131
msgid "Simplified Chinese"
msgstr "Simplified Chinese"

#: onemusic_backend/settings.py:132
msgid "Arabic"
msgstr ""

#: onemusic_backend/settings.py:133
msgid "German"
msgstr ""

#: onemusic_backend/settings.py:134
msgid "English"
msgstr "English"

#: onemusic_backend/settings.py:135
msgid "Spanish"
msgstr ""

#: onemusic_backend/settings.py:136
msgid "French"
msgstr ""

#: onemusic_backend/settings.py:137
msgid "Hindi"
msgstr ""

#: onemusic_backend/settings.py:138
msgid "Italian"
msgstr ""

#: onemusic_backend/settings.py:139
msgid "Japanese"
msgstr ""

#: onemusic_backend/settings.py:140
msgid "Korean"
msgstr ""

#: onemusic_backend/settings.py:141
msgid "Dutch"
msgstr ""

#: onemusic_backend/settings.py:142
msgid "Portuguese"
msgstr ""

#: onemusic_backend/settings.py:143
msgid "Russian"
msgstr ""

#: onemusic_backend/settings.py:144
msgid "Turkish"
msgstr ""

#: onemusic_backend/settings.py:145
msgid "Traditional Chinese"
msgstr ""
