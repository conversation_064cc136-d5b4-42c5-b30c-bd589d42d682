'use client'
import {createContext, useEffect, useLayoutEffect, useState, useRef} from 'react';
import {
    Box,
    AppBar,
    Toolbar,
    Typography,
    IconButton,
    TextField,
    InputAdornment,
    Avatar, Select, MenuItem, ListItemIcon, Menu, createTheme, CssBaseline, useMediaQuery
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import GTranslateIcon from '@mui/icons-material/GTranslate';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';

import { styled } from '@mui/material/styles';
import { ThemeProvider } from '@mui/material/styles';

import {MusicCardContainer} from "@/src/app/[locale]/component/musicContainer";
import AudioPlayer from "@/src/app/[locale]/component/audioPlayer";
import axios from "axios";
import {NotificationsProvider} from "@toolpad/core";
import NoSsr from "@mui/material/NoSsr";
import {backendUrl, languages} from "@/src/app/[locale]/config";
import {useCookies} from "react-cookie";
import {useRouter} from "next/navigation";
import {useLocale, useTranslations} from "next-intl";
import {Turnstile} from "@marsidev/react-turnstile";


export const GlobalContext = createContext({});

const StyledAppBar = styled(AppBar)(({ theme }) => ({
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.primary,
    boxShadow: 'none',
    borderBottom: `1px solid ${theme.palette.divider}`,
}));

const theme = createTheme({
    colorSchemes: {
        dark: true,
    },
});

function stringToColor(string) {
    let hash = 0;
    let i;

    /* eslint-disable no-bitwise */
    for (i = 0; i < string.length; i += 1) {
        hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = '#';

    for (i = 0; i < 3; i += 1) {
        const value = (hash >> (i * 8)) & 0xff;
        color += `00${value.toString(16)}`.slice(-2);
    }
    /* eslint-enable no-bitwise */

    return color;
}

export function stringAvatar(name) {
    return {
        sx: {
            bgcolor:  name ? stringToColor(name) : null,
        },
        children: name ? name[0] : null,
    };
}

export default function Home() {
    const [cookie, setCookie] = useCookies(['username','NEXT_LOCALE']);
    const [webdavConfigs, setWebdavConfigs] = useState([]);
    const [webdavJwt, setWebdavJwt] = useState(null); // WebDAV JWT令牌
    const [searchValue, setSearchValue] = useState('');
    const [loading, setLoading] = useState(false);
    const [songsData, setSongData] = useState([]);
    const [isAudioLoading, setAudioLoading] = useState(false);
    const [playingSongData, setPlayingSongData] = useState(null);
    const [searchType, setSearchType] = useState('songs');
    const [anchorEl, setAnchorEl] = useState(null);
    const [premium,setPremium] = useState(true)
    const [token, setToken] = useState(null)
    const [languageAnchorEl, setLanguageAnchorEl] = useState(null);
    const languageOpen = Boolean(languageAnchorEl);
    const t = useTranslations('Home')
    const router = useRouter();
    const locale = useLocale()
    const open = Boolean(anchorEl);
    const ref = useRef()
    const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
    const coverHeight = isSmallScreen ? '70px' : '80px'

    const handleLanguageClick = (event) => {
        setLanguageAnchorEl(event.currentTarget);
    };

    const handleLanguageChange = (locale) => {
        setCookie('NEXT_LOCALE', locale, { path: '/' });
        setLanguageAnchorEl(null);
        router.push("/" + locale)
    };

    const handleSearchSubmit = async (event) => {
        event.preventDefault();
        if (searchValue.trim()) {
            setLoading(true);
            setToken(null)
            try {
                const response = await axios.get('https://api.1music.cc/search', {
                    withCredentials: true,
                    params: { [searchType]: searchValue, token: token },
                });
                setSongData(response.data);
            } catch (error) {
                console.error('Search error:', error);
            } finally {
                setLoading(false);
                ref.current?.reset()
            }
        }
    };

    const fetchRandomMusic = async () => {
        try {
            const response = await fetch(backendUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
            });

            if (!response.ok) {
                throw new Error('获取随机歌曲失败');
            }

            const data = await response.json();
            setSongData(data.music_list);
        } catch (error) {
            console.error('Error fetching random music:', error);
        }
    };

    useLayoutEffect(() => {
        if (typeof window !== "undefined") {
            let userLang = cookie['NEXT_LOCALE'] || navigator.language || navigator.userLanguage;

            // 处理繁体中文
            if (userLang === "zh-HK" || userLang === "zh-MO") {
                userLang = "zh-TW";
            }

            // 只获取 `语言代码`（比如 `en-US` => `en`）
            let langPrefix = userLang.split("-")[0]; // 提取前缀，如 `en`, `zh`, `fr`

            // 定义支持的语言
            const supportedLocales = Object.keys(languages);

            // 如果 `userLang` 直接匹配支持的语言，则使用完整的 `userLang`
            let detectedLocale = supportedLocales.includes(userLang)
                ? userLang
                : supportedLocales.includes(langPrefix)
                    ? langPrefix  // 如果完整匹配失败，使用前缀匹配
                    : "en"; // 默认回退到 `zh_CN`

            // 只有当检测到的语言与当前 `locale` 不同时才切换
            if (locale !== detectedLocale) {
                router.replace(`/${detectedLocale}`);
            }
        }

        fetch(backendUrl + 'get_user_profile/',{
            credentials:'include'
        })
            .then(response => {
                if (response.redirected) {
                }
                return response.json()
            })
            .then(data => {
                setPremium(data.points && data.points > 100)
                setCookie('username',data.username || null)
                setWebdavConfigs(data.webdav_configs || []);
                setWebdavJwt(data.webdav_jwt); // 保存WebDAV JWT
            })
            .catch(err => {
                setPremium(false)
                console.log(err)
            });

        fetchRandomMusic();

    }, []);

    useEffect(() => {
        if (premium !== true) {
            console.log(' ')
        }
    }, [premium]);

    const handleAvatarClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    return (
        <NoSsr>
            <NotificationsProvider>
                <GlobalContext.Provider value={{
                    setPlayingSongData,
                    setAudioLoading,
                    webdavConfigs,
                    webdavJwt,  // 传递WebDAV JWT
                    premium
                }}>
                    <ThemeProvider theme={theme} noSsr><CssBaseline />
                    <StyledAppBar position="static">
                        <Toolbar>
                            {/* 小屏幕时头像在左边 */}
                            {isSmallScreen && (
                                <IconButton color="inherit" onClick={handleAvatarClick} sx={{ mr: 1 }}>
                                    <Avatar {...stringAvatar(cookie['username'] ? cookie['username'] : null)} />
                                </IconButton>
                            )}

                            {/* 大屏幕时显示 logo */}
                            {!isSmallScreen && (
                                <Typography variant="h6" onClick={() => fetchRandomMusic()} component="div" sx={{
                                    flexGrow: 0.1,
                                    fontWeight: 'bold',
                                    fontFamily: "HarmonyOS Sans,system-ui",
                                    letterSpacing: -0.5
                                }}>
                                    1Music.cc
                                </Typography>
                            )}

                            <Box sx={{ mx: isSmallScreen ? 0 : 2, display: 'flex', gap: '8px', flexGrow: 1 }}>
                                <form onSubmit={handleSearchSubmit} style={{ alignItems: 'center', flexGrow: 1 }}>
                                    <TextField
                                        fullWidth
                                        disabled={!token}
                                        placeholder={token ? t("searchPlaceholder") : t("verificationPending")}
                                        value={searchValue}
                                        onChange={(e) => setSearchValue(e.target.value)}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <SearchIcon />
                                                </InputAdornment>
                                            ),
                                        }}
                                        variant="outlined"
                                        size="small"
                                    />
                                </form>
                                <Select
                                    value={searchType}
                                    onChange={(e) => setSearchType(e.target.value)}
                                    variant="outlined"
                                    size="small"
                                >
                                    <MenuItem value="songs">Songs</MenuItem>
                                    <MenuItem value="artists">Artists</MenuItem>
                                </Select>
                            </Box>

                            {/* 大屏幕时头像在右边 */}
                            {!isSmallScreen && (
                                <IconButton color="inherit" onClick={handleAvatarClick}>
                                    <Avatar {...stringAvatar(cookie['username'] ? cookie['username'] : null)} />
                                </IconButton>
                            )}

                            {/* Menu 组件始终存在，但只有在点击头像时才显示 */}
                            <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
                                <MenuItem onClick={() => router.replace(cookie['username'] ? '/user' : '/login')}>
                                    <ListItemIcon><AccountCircleIcon /></ListItemIcon>
                                    {cookie['username'] ? cookie['username'] : t("login")}
                                </MenuItem>
                                <MenuItem onClick={handleLanguageClick}>
                                    <ListItemIcon><GTranslateIcon /></ListItemIcon>
                                    {t("setLanguage")}
                                </MenuItem>
                                <Menu anchorEl={languageAnchorEl} open={languageOpen} onClose={() => setLanguageAnchorEl(null)}>
                                    {Object.entries(languages).map(([langCode, langName]) => (
                                        <MenuItem key={langCode} onClick={() => handleLanguageChange(langCode)}>
                                            {langName}
                                        </MenuItem>
                                    ))}
                                </Menu>
                                {cookie['username'] && <MenuItem onClick={() => router.replace(cookie['username'] ? '/user' : '/login')}>
                                    <ListItemIcon><ManageAccountsIcon /></ListItemIcon>
                                    {t("manageUser")}
                                </MenuItem>}
                            </Menu>
                        </Toolbar>
                    </StyledAppBar>
                    <MusicCardContainer songs={songsData} loading={loading}></MusicCardContainer>
                    <Turnstile
                        ref={ref}
                        siteKey="0x4AAAAAABBxNT2ftEWvsRrE"
                        size="invisible"
                        onSuccess={(token) => setToken(token)}
                    />
                    <Box sx={{ mt: 0.5, textAlign: "center" }}>
                        <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                            Contact me: <EMAIL>
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                            &copy; 2025 1Music.cc All rights reserved.
                        </Typography>
                    </Box>
                    <Box sx={{ mt:1,height: coverHeight }}></Box>
                    <AudioPlayer coverHeight={coverHeight} songData={playingSongData}></AudioPlayer>
                    </ThemeProvider>
                </GlobalContext.Provider>
            </NotificationsProvider>
        </NoSsr>
    );
}




