import axios from "axios";
import {backendUrl} from "@/src/app/[locale]/config";

export function getCookie(name,cookies) {
    const cookie = (cookies ? cookies : document.cookie).split(';')
    for (let i = 0; i < cookie.length; i++) {
        const cookie_ = cookie[i].trim();
        const cookieParts = cookie_.split('=');

        if (cookieParts[0] === name) {
            return cookieParts[1];
        }
    }

    return null;
}

export const fetchDownloadStatus = async (songData,path) => {
    try {
        const response = await axios.post(backendUrl + `${path}/`, songData,{
            withCredentials: true
        });
        return response.data; // 返回 { detail: '...', download_url: '...', status: 200 或 202 }
    } catch (error) {
        console.log(error)
        if (error.status >= 300 && error.status < 400) {
            window.location.href = error.headers.Location
        }
        throw error.response ? error.response.data : new Error('无法连接到服务器');
    }
};

// 检查下载链接是否可用
export const checkDownloadLink = async (url) => {
    try {
        const response = await axios.head(url);
        return response.status === 200;
    } catch {
        return false;
    }
};

// 创建 axios 实例
const axiosInstance = axios.create();

// 请求拦截器：可以用于配置请求（可选）
axiosInstance.interceptors.request.use(config => {
    return config;
}, error => {
    return Promise.reject(error);
});

// 响应拦截器：处理重定向
axiosInstance.interceptors.response.use(response => {
    // 检查是否是 3xx 状态码（例如 301、302 等）
    if (response.status >= 300 && response.status < 400) {
        const redirectUrl = response.headers.Location;
        console.log('重定向地址:', redirectUrl);

        // 修改页面的 href 属性以进行重定向
        window.location.href = redirectUrl;

        // 返回一个 rejected promise，防止其他逻辑继续执行
        return Promise.reject('页面已重定向');
    }

    return response;  // 如果没有重定向，直接返回响应
}, error => {
    return Promise.reject(error);
});

export default axiosInstance
